#!/usr/bin/env python3
"""
DYNAMIC Memory Scanner
======================

Finds REAL memory addresses while you're IN GAME.
Since you're paused in zombies, this will find your actual health/ammo/position.

Author: Enhanced System
Version: REAL SCANNER 1.0
"""

import psutil
import ctypes
import struct
import time

class DynamicScanner:
    """Scan for real memory addresses while in game"""
    
    def __init__(self):
        # Find COD WWII
        self.process_id = None
        for proc in psutil.process_iter(['pid', 'name']):
            if 's2_mp64_ship.exe' in proc.info['name']:
                self.process_id = proc.info['pid']
                break
        
        if not self.process_id:
            print("[SCANNER] COD WWII not found!")
            return
        
        # Get process handle
        self.process_handle = ctypes.windll.kernel32.OpenProcess(0x1F0FFF, False, self.process_id)
        print(f"[SCANNER] Attached to COD WWII PID {self.process_id}")
    
    def scan_for_health_100(self):
        """Scan for health value 100 (starting zombies health)"""
        print("[SCANNER] Scanning for health value 100...")
        print("[SCANNER] Make sure your zombies health is 100!")
        
        found_addresses = []
        
        # Scan memory ranges
        memory_ranges = [
            (0x10000000, 0x20000000),   # 256MB-512MB range
            (0x140000000, 0x150000000), # Your suspected range
            (0x7FF00000000, 0x7FF10000000), # High 64-bit range
        ]
        
        for start_addr, end_addr in memory_ranges:
            print(f"[SCANNER] Scanning range 0x{start_addr:X} - 0x{end_addr:X}")
            
            for addr in range(start_addr, end_addr, 0x1000):  # 4KB steps
                try:
                    value = self.read_int(addr)
                    if value == 100:
                        # Found potential health
                        found_addresses.append(addr)
                        print(f"  Found 100 at 0x{addr:X}")
                        
                        if len(found_addresses) >= 20:  # Stop after 20 findings
                            break
                            
                except Exception:
                    continue
            
            if len(found_addresses) >= 20:
                break
        
        print(f"\n[SCANNER] Found {len(found_addresses)} potential health addresses")
        
        if found_addresses:
            print("[SCANNER] Now take some damage and run second scan...")
            return found_addresses
        else:
            print("[SCANNER] No health values found - are you sure health is 100?")
            return []
    
    def scan_for_health_after_damage(self, previous_addresses, new_health):
        """Scan previous addresses for new health value after taking damage"""
        print(f"[SCANNER] Scanning for health value {new_health}...")
        
        real_health_addresses = []
        
        for addr in previous_addresses:
            try:
                value = self.read_int(addr)
                if value == new_health:
                    real_health_addresses.append(addr)
                    print(f"  REAL HEALTH ADDRESS: 0x{addr:X} = {value}")
            except Exception:
                continue
        
        print(f"\n[SCANNER] Found {len(real_health_addresses)} REAL health addresses!")
        
        if real_health_addresses:
            # Test writing to first address
            test_addr = real_health_addresses[0]
            print(f"[SCANNER] Testing write to 0x{test_addr:X}...")
            
            original_health = self.read_int(test_addr)
            self.write_int(test_addr, 999)
            time.sleep(0.1)
            new_value = self.read_int(test_addr)
            
            if new_value == 999:
                print(f"[SCANNER] SUCCESS! Health write works at 0x{test_addr:X}")
                print(f"[SCANNER] Your REAL health address: 0x{test_addr:X}")
                return real_health_addresses
            else:
                print(f"[SCANNER] Write failed: {original_health} -> {new_value}")
        
        return real_health_addresses
    
    def find_ammo_addresses(self):
        """Find ammo addresses by scanning for common ammo values"""
        print("[SCANNER] Scanning for ammo values...")
        print("[SCANNER] Check your current ammo count and tell me the number!")
        
        # Common ammo values to scan for
        ammo_values = [30, 150, 240, 8, 12, 24, 32, 60, 90, 120, 180, 210]
        
        found_ammo_addresses = []
        
        for ammo_val in ammo_values:
            print(f"[SCANNER] Scanning for ammo value: {ammo_val}")
            
            # Scan smaller range around typical game memory
            for addr in range(0x140000000, 0x150000000, 0x4):  # 4-byte aligned
                try:
                    value = self.read_int(addr)
                    if value == ammo_val:
                        found_ammo_addresses.append((addr, ammo_val))
                        print(f"  Found {ammo_val} at 0x{addr:X}")
                        
                        if len(found_ammo_addresses) >= 50:  # Limit results
                            break
                            
                except Exception:
                    continue
                
                # Progress indicator
                if addr % 0x100000 == 0:  # Every 1MB
                    print(f"  Scanning... 0x{addr:X}")
        
        return found_ammo_addresses
    
    def interactive_health_scan(self):
        """Interactive health scanning"""
        print("\n" + "=" * 60)
        print("INTERACTIVE HEALTH SCANNER")
        print("=" * 60)
        print("")
        print("STEP 1: Make sure you're IN Nazi Zombies with FULL HEALTH")
        print("STEP 2: Pause the game")
        print("STEP 3: Run this scan")
        print("")
        
        input("Press Enter when ready for first scan...")
        
        # First scan
        first_addresses = self.scan_for_health_100()
        
        if not first_addresses:
            print("No addresses found. Are you sure you have 100 health?")
            return
        
        print(f"\nFound {len(first_addresses)} potential addresses")
        print("Now take some damage (to like 75 health) and pause again...")
        
        new_health = input("What's your health now? (e.g., 75): ")
        try:
            new_health = int(new_health)
        except ValueError:
            print("Invalid health value")
            return
        
        # Second scan
        real_addresses = self.scan_for_health_after_damage(first_addresses, new_health)
        
        if real_addresses:
            print(f"\n🎯 FOUND YOUR REAL HEALTH ADDRESS!")
            print(f"Address: 0x{real_addresses[0]:X}")
            print(f"This is your REAL health location in memory!")
            
            # Extract base address and offset
            health_addr = real_addresses[0]
            print(f"\nYour REAL health address: 0x{health_addr:X}")
            print("Now you can use this for godmode!")
            
            return real_addresses[0]
        else:
            print("No real health addresses found. Try again with different health values.")
            return None
    
    def read_int(self, address):
        try:
            buffer = ctypes.create_string_buffer(4)
            bytes_read = ctypes.c_size_t(0)
            result = ctypes.windll.kernel32.ReadProcessMemory(
                self.process_handle, ctypes.c_void_p(address),
                buffer, 4, ctypes.byref(bytes_read))
            if result and bytes_read.value == 4:
                return struct.unpack('<i', buffer.raw)[0]
        except Exception:
            pass
        return 0
    
    def write_int(self, address, value):
        try:
            data = struct.pack('<i', value)
            bytes_written = ctypes.c_size_t(0)
            result = ctypes.windll.kernel32.WriteProcessMemory(
                self.process_handle, ctypes.c_void_p(address),
                data, len(data), ctypes.byref(bytes_written))
            return result and bytes_written.value == len(data)
        except Exception:
            return False

if __name__ == "__main__":
    scanner = DynamicScanner()
    if scanner.process_handle:
        # Run interactive scan
        health_addr = scanner.interactive_health_scan()
        
        if health_addr:
            print(f"\n🎉 SUCCESS! Your health is at 0x{health_addr:X}")
            print("Use this address for REAL godmode!")
    else:
        print("Failed to attach to COD WWII")





