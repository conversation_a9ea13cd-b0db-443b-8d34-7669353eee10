#!/usr/bin/env python3
"""
Minimal Safe Test
=================

Ultra-safe memory modification that definitely won't crash the game.
Only modifies 1-2 addresses very carefully.

Author: Enhanced System
Version: MINIMAL SAFE 1.0
"""

import ctypes
import ctypes.wintypes
import psutil
import time
import struct

class MinimalSafeTest:
    """Minimal safe memory modification"""
    
    def __init__(self):
        self.process_id = None
        self.process_handle = None
        self.base_address = None
        self.test_address = None
        self.original_value = None
        self.active = False
    
    def find_process(self):
        """Find COD WWII process"""
        for proc in psutil.process_iter(['pid', 'name']):
            if 's2_mp64_ship.exe' in proc.info['name']:
                self.process_id = proc.info['pid']
                print(f"✅ Found COD WWII: PID {self.process_id}")
                return True
        
        print("❌ COD WWII not found!")
        return False
    
    def get_handle(self):
        """Get minimal process handle"""
        try:
            self.process_handle = ctypes.windll.kernel32.OpenProcess(
                0x0038, False, self.process_id)  # Minimal permissions
            
            if self.process_handle:
                print("✅ Process handle acquired")
                return True
            else:
                print("❌ Failed to get handle")
                return False
        except Exception as e:
            print(f"❌ Handle error: {e}")
            return False
    
    def find_base(self):
        """Find base address"""
        try:
            hModules = (ctypes.wintypes.HMODULE * 1024)()
            cbNeeded = ctypes.wintypes.DWORD()
            
            result = ctypes.windll.psapi.EnumProcessModules(
                self.process_handle, ctypes.byref(hModules), 
                ctypes.sizeof(hModules), ctypes.byref(cbNeeded)
            )
            
            if result:
                self.base_address = hModules[0]
                print(f"✅ Base address: 0x{self.base_address:X}")
                return True
            else:
                print("❌ Failed to get base address")
                return False
                
        except Exception as e:
            print(f"❌ Base error: {e}")
            return False
    
    def read_int(self, address):
        """Read integer safely"""
        try:
            buffer = ctypes.create_string_buffer(4)
            bytes_read = ctypes.c_size_t(0)
            
            result = ctypes.windll.kernel32.ReadProcessMemory(
                self.process_handle, ctypes.c_void_p(address),
                buffer, 4, ctypes.byref(bytes_read)
            )
            
            if result and bytes_read.value == 4:
                return struct.unpack('<i', buffer.raw)[0]
            return None
        except:
            return None
    
    def write_int(self, address, value):
        """Write integer safely"""
        try:
            data = struct.pack('<i', value)
            bytes_written = ctypes.c_size_t(0)
            
            result = ctypes.windll.kernel32.WriteProcessMemory(
                self.process_handle, ctypes.c_void_p(address),
                data, len(data), ctypes.byref(bytes_written)
            )
            
            return result and bytes_written.value == len(data)
        except:
            return False
    
    def find_safe_test_address(self):
        """Find ONE safe address to test with"""
        print("🔍 Looking for ONE safe test address...")
        
        # Look in a safe area (not near critical code)
        scan_start = self.base_address + 0x2000000  # Skip first 32MB
        scan_end = self.base_address + 0x2100000    # Only scan 1MB
        
        for addr in range(scan_start, scan_end, 0x1000):  # 4KB steps
            try:
                value = self.read_int(addr)
                
                if value is not None and 1 <= value <= 100:
                    # Test if we can write to it
                    if self.write_int(addr, value + 1):
                        time.sleep(0.01)
                        
                        # Check if write worked
                        new_value = self.read_int(addr)
                        if new_value == value + 1:
                            # Restore original immediately
                            self.write_int(addr, value)
                            
                            # This is our test address
                            self.test_address = addr
                            self.original_value = value
                            print(f"✅ Found safe test address: 0x{addr:X} (value: {value})")
                            return True
                            
            except:
                continue
        
        print("❌ No safe test address found")
        return False
    
    def start_minimal_test(self):
        """Start minimal safe test"""
        if self.active or not self.test_address:
            return
        
        self.active = True
        print("🧪 Starting minimal safe test...")
        print("This will modify ONE address very carefully")
        
        test_count = 0
        
        while self.active and test_count < 10:  # Only run 10 times
            try:
                # Read current value
                current = self.read_int(self.test_address)
                
                if current is not None:
                    # If it's the original value, set it higher
                    if current == self.original_value:
                        new_value = min(self.original_value + 50, 150)  # Safe increase
                        if self.write_int(self.test_address, new_value):
                            print(f"✅ Test {test_count + 1}: {current} → {new_value}")
                        else:
                            print(f"❌ Test {test_count + 1}: Write failed")
                    else:
                        print(f"ℹ️  Test {test_count + 1}: Value is {current} (modified by game)")
                else:
                    print(f"❌ Test {test_count + 1}: Read failed")
                
                test_count += 1
                time.sleep(2.0)  # Very slow, safe updates
                
            except Exception as e:
                print(f"❌ Test error: {e}")
                break
        
        # Restore original value
        if self.test_address and self.original_value is not None:
            self.write_int(self.test_address, self.original_value)
            print(f"✅ Restored original value: {self.original_value}")
        
        self.active = False
        print("🧪 Minimal test completed safely")
    
    def stop_test(self):
        """Stop test and restore"""
        self.active = False
        
        if self.test_address and self.original_value is not None:
            self.write_int(self.test_address, self.original_value)
            print(f"✅ Test stopped - restored original value: {self.original_value}")

def main():
    """Main test function"""
    print("=" * 50)
    print("MINIMAL SAFE COD WWII TEST")
    print("=" * 50)
    print("This will modify ONLY ONE address very carefully")
    print("Extremely unlikely to crash the game")
    print()
    
    test = MinimalSafeTest()
    
    # Initialize
    if not test.find_process():
        return
    
    if not test.get_handle():
        return
    
    if not test.find_base():
        return
    
    if not test.find_safe_test_address():
        print("\n❌ Could not find a safe address to test")
        print("This might mean:")
        print("  • Game is not in a suitable state")
        print("  • Memory protection is too strong")
        print("  • Need to be in an active match")
        return
    
    print("\n" + "=" * 50)
    print("READY FOR MINIMAL SAFE TEST")
    print("=" * 50)
    
    while True:
        print("\nOptions:")
        print("1. Start minimal test (modifies 1 address)")
        print("2. Stop test and restore")
        print("3. Check current value")
        print("4. Exit")
        
        choice = input("\nChoice: ").strip()
        
        if choice == '1':
            if not test.active:
                test.start_minimal_test()
            else:
                print("Test already running")
        elif choice == '2':
            test.stop_test()
        elif choice == '3':
            if test.test_address:
                current = test.read_int(test.test_address)
                print(f"Current value at 0x{test.test_address:X}: {current}")
                print(f"Original value was: {test.original_value}")
            else:
                print("No test address found")
        elif choice == '4':
            test.stop_test()
            print("Goodbye!")
            break
        else:
            print("Invalid choice")

if __name__ == "__main__":
    main()
