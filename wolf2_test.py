#!/usr/bin/env python3
"""
Wolfenstein 2 Connection Test
=============================

Simple test to verify we can connect to Wolfenstein 2 and access memory.

Author: Enhanced System
Version: 1.0
"""

import pymem
import pymem.process
import psutil
import time

def find_wolfenstein_process():
    """Find Wolfenstein 2 process"""
    possible_names = [
        "BJ2_x64vk.exe",      # Vulkan version (Steam default)
        "BJ2_x64gl.exe",      # OpenGL version
        "BJ2.exe",            # Generic
        "Wolfenstein2.exe",   # Alternative
        "NewColossus.exe",    # Alternative
        "WolfII.exe"          # Another alternative
    ]
    
    print("🔍 Searching for Wolfenstein 2 process...")
    
    for proc in psutil.process_iter(['pid', 'name', 'exe']):
        try:
            proc_name = proc.info['name']
            
            for target_name in possible_names:
                if target_name.lower() in proc_name.lower():
                    print(f"✅ Found Wolfenstein 2:")
                    print(f"   Process: {proc_name}")
                    print(f"   PID: {proc.info['pid']}")
                    print(f"   Path: {proc.info.get('exe', 'Unknown')}")
                    return proc_name, proc.info['pid']
                    
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    print("❌ Wolfenstein 2 not found!")
    print("Make sure the game is running and try these process names:")
    for name in possible_names:
        print(f"   - {name}")
    
    return None, None

def test_memory_connection(process_name):
    """Test memory connection"""
    try:
        print(f"\n🔌 Connecting to {process_name}...")
        pm = pymem.Pymem(process_name)
        
        print(f"✅ Connected successfully!")
        print(f"   Base Address: 0x{pm.base_address:X}")
        
        # Test basic memory reading
        print(f"\n🧪 Testing memory access...")
        
        # Read from base address
        try:
            value = pm.read_int(pm.base_address)
            print(f"   Base value: {value} (0x{value:X})")
            print("✅ Memory reading works!")
            
            # Test memory scanning for health-like values
            print(f"\n🔍 Scanning for potential game values...")
            
            scan_start = pm.base_address + 0x1000000  # Skip first 16MB
            scan_size = 0x1000000  # Scan 16MB
            
            health_candidates = []
            
            for addr in range(scan_start, scan_start + scan_size, 0x10000):
                try:
                    value = pm.read_int(addr)
                    
                    # Look for health-like values (50-200 range)
                    if 50 <= value <= 200:
                        health_candidates.append((addr, value))
                        
                        if len(health_candidates) >= 10:
                            break
                            
                except:
                    continue
            
            print(f"   Found {len(health_candidates)} potential health addresses:")
            for addr, value in health_candidates[:5]:
                offset = addr - pm.base_address
                print(f"     0x{addr:X} (base+0x{offset:X}) = {value}")
            
            if health_candidates:
                # Test writing to first candidate
                test_addr, original_value = health_candidates[0]
                print(f"\n✏️  Testing memory write at 0x{test_addr:X}...")
                
                try:
                    # Write test value
                    test_value = original_value + 10
                    pm.write_int(test_addr, test_value)
                    time.sleep(0.1)
                    
                    # Read back
                    new_value = pm.read_int(test_addr)
                    
                    # Restore original
                    pm.write_int(test_addr, original_value)
                    
                    if new_value == test_value:
                        print(f"✅ Memory writing works!")
                        print(f"   {original_value} → {test_value} → {original_value}")
                        return True
                    else:
                        print(f"⚠️  Write partially worked: {original_value} → {new_value}")
                        
                except Exception as e:
                    print(f"❌ Memory write failed: {e}")
            
            return True
            
        except Exception as e:
            print(f"❌ Memory reading failed: {e}")
            return False
            
    except Exception as e:
        print(f"❌ Connection failed: {e}")
        return False

def main():
    """Main test function"""
    print("=" * 60)
    print("WOLFENSTEIN 2 CONNECTION TEST")
    print("=" * 60)
    print()
    
    # Find process
    process_name, pid = find_wolfenstein_process()
    
    if not process_name:
        print("\n💡 To fix this:")
        print("1. Start Wolfenstein 2: The New Colossus")
        print("2. Get to the main menu or in-game")
        print("3. Run this test again")
        return
    
    # Test connection
    if test_memory_connection(process_name):
        print("\n" + "=" * 60)
        print("🎉 CONNECTION TEST SUCCESSFUL!")
        print("=" * 60)
        print("✅ Process found")
        print("✅ Memory access working")
        print("✅ Read/write operations successful")
        print("\nYou can now run the main hack program:")
        print("python wolfenstein2_hacks.py")
    else:
        print("\n" + "=" * 60)
        print("❌ CONNECTION TEST FAILED")
        print("=" * 60)
        print("The game might have memory protection enabled.")
        print("Try running as Administrator or check antivirus settings.")

if __name__ == "__main__":
    main()
