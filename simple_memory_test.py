#!/usr/bin/env python3
"""
Simple Memory Test
==================

Test memory access without gamepad dependencies.
Focus on getting the core memory reading/writing working first.

Author: Enhanced System
Version: 1.0
"""

import pymem
import pymem.process
import time
import threading

class SimpleMemoryTest:
    """Simple memory test without gamepad"""
    
    def __init__(self):
        self.pm = None
        self.base_address = None
        self.godmode_active = False
        
        # CORRECT July 2025 Nazi Zombies offsets
        self.offsets = {
            'health_base': 0xA2D7DC8,
            'health_offset': 0x2DC,
            'entity_list': 0xA0C7130,
            'entity_size': 0x418,
            'current_ammo_base': 0x10948C8,
            'current_ammo_offset': 0x8,
        }
    
    def connect(self):
        """Connect to COD WWII"""
        try:
            self.pm = pymem.Pymem("s2_mp64_ship.exe")
            self.base_address = self.pm.base_address
            print(f"✅ Connected to COD WWII")
            print(f"   Base Address: 0x{self.base_address:X}")
            return True
        except Exception as e:
            print(f"❌ Connection failed: {e}")
            print("   Make sure COD WWII is running!")
            return False
    
    def test_memory_reading(self):
        """Test reading memory with correct offsets"""
        print("\n🔍 Testing memory reading...")
        
        try:
            # Test 1: Health pointer
            health_ptr_addr = self.base_address + self.offsets['health_base']
            health_ptr = self.pm.read_int(health_ptr_addr)
            print(f"   Health pointer address: 0x{health_ptr_addr:X}")
            print(f"   Health pointer value: 0x{health_ptr:X}")
            
            if health_ptr > 0x10000:
                # Try to read health
                health_addr = health_ptr + self.offsets['health_offset']
                health = self.pm.read_int(health_addr)
                print(f"   Health address: 0x{health_addr:X}")
                print(f"   Health value: {health}")
                
                if health > 0:
                    print("   ✅ Health reading WORKS!")
                    return True
                else:
                    print("   ⚠️  Health is 0 - might not be in active match")
            else:
                print("   ❌ Invalid health pointer")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
        
        return False
    
    def test_memory_writing(self):
        """Test writing to memory"""
        print("\n✏️  Testing memory writing...")
        
        try:
            # Get health address
            health_ptr_addr = self.base_address + self.offsets['health_base']
            health_ptr = self.pm.read_int(health_ptr_addr)
            
            if health_ptr > 0x10000:
                health_addr = health_ptr + self.offsets['health_offset']
                
                # Read current health
                old_health = self.pm.read_int(health_addr)
                print(f"   Current health: {old_health}")
                
                # Try to write new health
                new_health = 999
                self.pm.write_int(health_addr, new_health)
                
                # Verify write
                time.sleep(0.1)
                verified_health = self.pm.read_int(health_addr)
                print(f"   After write: {verified_health}")
                
                if verified_health == new_health:
                    print("   ✅ Memory writing WORKS!")
                    return True
                else:
                    print("   ❌ Memory write failed or was overwritten")
            else:
                print("   ❌ Invalid health pointer")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
        
        return False
    
    def start_simple_godmode(self):
        """Start simple godmode test"""
        if self.godmode_active:
            print("   Godmode already active")
            return
        
        self.godmode_active = True
        print("\n🛡️  Starting simple godmode...")
        
        def godmode_worker():
            while self.godmode_active:
                try:
                    health_ptr_addr = self.base_address + self.offsets['health_base']
                    health_ptr = self.pm.read_int(health_ptr_addr)
                    
                    if health_ptr > 0x10000:
                        health_addr = health_ptr + self.offsets['health_offset']
                        current_health = self.pm.read_int(health_addr)
                        
                        if 0 < current_health < 200:
                            self.pm.write_int(health_addr, 999)
                            print(f"   🛡️  Health: {current_health} → 999")
                    
                    time.sleep(0.5)  # Check every 500ms
                    
                except Exception as e:
                    print(f"   Godmode error: {e}")
                    time.sleep(2.0)
        
        threading.Thread(target=godmode_worker, daemon=True).start()
        print("   ✅ Godmode thread started")
    
    def stop_godmode(self):
        """Stop godmode"""
        self.godmode_active = False
        print("   🛡️  Godmode stopped")
    
    def scan_entity_list(self):
        """Scan entity list for enemies"""
        print("\n👥 Scanning entity list...")
        
        try:
            entity_base = self.base_address + self.offsets['entity_list']
            entities_found = 0
            
            for i in range(32):  # Max 32 entities
                entity_addr = entity_base + (self.offsets['entity_size'] * i)
                
                try:
                    # Read client number (first 2 bytes)
                    client_num = self.pm.read_short(entity_addr)
                    
                    if client_num > 0:
                        # Read health at offset 0x2DC
                        health = self.pm.read_int(entity_addr + 0x2DC)
                        
                        if health > 0:
                            entities_found += 1
                            print(f"   Entity {i}: Client={client_num}, Health={health}")
                            
                            if entities_found >= 5:  # Limit output
                                print("   ... (showing first 5)")
                                break
                                
                except:
                    continue
            
            print(f"   Found {entities_found} active entities")
            return entities_found > 0
            
        except Exception as e:
            print(f"   ❌ Entity scan error: {e}")
            return False

def main():
    """Main test function"""
    print("=" * 50)
    print("COD WWII SIMPLE MEMORY TEST")
    print("=" * 50)
    print()
    print("Instructions:")
    print("1. Make sure COD WWII is running")
    print("2. Be in an ACTIVE Nazi Zombies match")
    print("3. Have visible health on screen")
    print()
    
    test = SimpleMemoryTest()
    
    # Step 1: Connect
    if not test.connect():
        return
    
    # Step 2: Test reading
    if not test.test_memory_reading():
        print("\n❌ Memory reading failed!")
        print("   - Make sure you're in an ACTIVE Nazi Zombies match")
        print("   - Try pausing the game and running again")
        return
    
    # Step 3: Test writing
    if not test.test_memory_writing():
        print("\n❌ Memory writing failed!")
        return
    
    # Step 4: Test entity scanning
    test.scan_entity_list()
    
    # Step 5: Interactive menu
    print("\n" + "=" * 50)
    print("MEMORY ACCESS WORKING! 🎉")
    print("=" * 50)
    
    while True:
        print("\nOptions:")
        print("1. Start godmode")
        print("2. Stop godmode") 
        print("3. Test memory again")
        print("4. Scan entities")
        print("5. Exit")
        
        choice = input("\nChoice: ").strip()
        
        if choice == '1':
            test.start_simple_godmode()
        elif choice == '2':
            test.stop_godmode()
        elif choice == '3':
            test.test_memory_reading()
            test.test_memory_writing()
        elif choice == '4':
            test.scan_entity_list()
        elif choice == '5':
            test.stop_godmode()
            print("Goodbye!")
            break
        else:
            print("Invalid choice")

if __name__ == "__main__":
    main()
