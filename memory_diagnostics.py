#!/usr/bin/env python3
"""
Memory Diagnostics Tool
=======================

Figure out WHY the memory access isn't working and find the REAL offsets.

Author: Enhanced System
Version: 1.0
"""

import psutil
import ctypes
import ctypes.wintypes
import struct
import win32api
import win32process
import win32gui

class MemoryDiagnostics:
    """Diagnose memory access issues"""
    
    def __init__(self):
        self.process_id = None
        self.process_handle = None
        self.base_address = None
        self.modules = {}
        
    def find_cod_process(self):
        """Find COD WWII process and get detailed info"""
        print("[DIAG] Searching for COD WWII process...")
        
        for proc in psutil.process_iter(['pid', 'name', 'exe', 'cmdline']):
            try:
                if 's2_mp64_ship.exe' in proc.info['name']:
                    self.process_id = proc.info['pid']
                    
                    print(f"[DIAG] Found COD WWII:")
                    print(f"  - PID: {self.process_id}")
                    print(f"  - Name: {proc.info['name']}")
                    print(f"  - Exe: {proc.info['exe']}")
                    
                    # Get process details
                    process = psutil.Process(self.process_id)
                    print(f"  - CPU: {process.cpu_percent()}%")
                    print(f"  - Memory: {process.memory_info().rss / 1024 / 1024:.1f} MB")
                    print(f"  - Status: {process.status()}")
                    
                    return True
            except Exception:
                continue
        
        print("[DIAG] COD WWII not found!")
        return False
    
    def get_process_access(self):
        """Get process handle and check permissions"""
        print("[DIAG] Testing process access...")
        
        try:
            # Try different access levels
            access_levels = [
                (0x1F0FFF, "PROCESS_ALL_ACCESS"),
                (0x0400, "PROCESS_QUERY_INFORMATION"),
                (0x0010, "PROCESS_VM_READ"),
                (0x0020, "PROCESS_VM_WRITE"),
                (0x0030, "PROCESS_VM_READ | PROCESS_VM_WRITE")
            ]
            
            for access, name in access_levels:
                handle = ctypes.windll.kernel32.OpenProcess(access, False, self.process_id)
                if handle:
                    print(f"  SUCCESS: {name}")
                    if access == 0x1F0FFF:
                        self.process_handle = handle
                else:
                    print(f"  FAILED: {name}")
            
            return self.process_handle is not None
            
        except Exception as e:
            print(f"[DIAG] Process access error: {e}")
            return False
    
    def find_base_address(self):
        """Find the real base address of the game"""
        print("[DIAG] Finding real base address...")
        
        try:
            # Get module information
            process = psutil.Process(self.process_id)
            memory_maps = process.memory_maps()
            
            print(f"[DIAG] Process has {len(memory_maps)} memory regions")
            
            # Look for the main executable
            print("[DIAG] Searching through memory maps...")
            
            for i, mmap in enumerate(memory_maps):
                print(f"  Region {i}: {mmap.path} - {mmap.addr}")
                
                if mmap.path and 's2_mp64_ship.exe' in mmap.path:
                    # Parse address
                    addr_str = mmap.addr.split('-')[0]
                    base_addr = int(addr_str, 16)
                    print(f"  FOUND MAIN MODULE: {mmap.path}")
                    print(f"  Base address: 0x{base_addr:X}")
                    print(f"  Size: {mmap.rss}")
                    
                    self.base_address = base_addr
                    return True
                
                # Stop after checking 20 regions to avoid spam
                if i >= 20:
                    print(f"  ... (showing first 20 of {len(memory_maps)} regions)")
                    break
            
            print("[DIAG] Could not find main module base address")
            return False
            
        except Exception as e:
            print(f"[DIAG] Base address detection error: {e}")
            return False
    
    def test_memory_reading(self):
        """Test if we can actually read memory"""
        print("[DIAG] Testing memory reading capabilities...")
        
        if not self.process_handle or not self.base_address:
            print("[DIAG] No process handle or base address")
            return False
        
        try:
            # Test reading at base address
            test_addresses = [
                self.base_address,
                self.base_address + 0x1000,
                self.base_address + 0x2000,
                self.base_address + 0x3000
            ]
            
            for addr in test_addresses:
                try:
                    value = self.read_int(addr)
                    print(f"  0x{addr:X}: {value} (0x{value:X})")
                except Exception as e:
                    print(f"  0x{addr:X}: READ FAILED - {e}")
            
            return True
            
        except Exception as e:
            print(f"[DIAG] Memory reading test failed: {e}")
            return False
    
    def scan_for_health_pattern(self):
        """Scan memory for potential health values"""
        print("[DIAG] Scanning for health patterns (looking for values 1-100)...")
        
        if not self.process_handle or not self.base_address:
            return
        
        try:
            # Scan small chunks of memory for health-like values
            scan_size = 0x100000  # 1MB chunks
            
            for offset in range(0, 0x10000000, scan_size):  # Scan 256MB
                try:
                    addr = self.base_address + offset
                    
                    # Read chunk
                    buffer = ctypes.create_string_buffer(scan_size)
                    bytes_read = ctypes.c_size_t(0)
                    
                    result = ctypes.windll.kernel32.ReadProcessMemory(
                        self.process_handle, ctypes.c_void_p(addr),
                        buffer, scan_size, ctypes.byref(bytes_read)
                    )
                    
                    if result and bytes_read.value > 0:
                        # Look for potential health values
                        data = buffer.raw[:bytes_read.value]
                        
                        for i in range(0, len(data) - 4, 4):
                            value = struct.unpack('<i', data[i:i+4])[0]
                            
                            # Look for values that could be health (1-100)
                            if 50 <= value <= 100:
                                found_addr = addr + i
                                print(f"  Potential health at 0x{found_addr:X}: {value}")
                    
                except Exception:
                    continue
                
                # Only scan first few chunks for performance
                if offset > 0x1000000:  # Stop after 16MB
                    break
            
        except Exception as e:
            print(f"[DIAG] Pattern scan error: {e}")
    
    def check_game_mode(self):
        """Check what game mode is active"""
        print("[DIAG] Checking game mode...")
        
        try:
            # Get window title for more info
            def enum_windows_callback(hwnd, windows):
                if win32gui.IsWindowVisible(hwnd):
                    try:
                        _, window_pid = win32process.GetWindowThreadProcessId(hwnd)
                        if window_pid == self.process_id:
                            title = win32gui.GetWindowText(hwnd)
                            if title:
                                windows.append(title)
                    except Exception:
                        pass
                return True
            
            windows = []
            win32gui.EnumWindows(enum_windows_callback, windows)
            
            for title in windows:
                print(f"  Window title: {title}")
                
                # Check for game mode indicators
                if 'Nazi Zombies' in title or 'Zombies' in title:
                    print("  🧟 DETECTED: Nazi Zombies mode")
                    print("  ⚠️  Zombie mode might have different memory layout!")
                elif 'Multiplayer' in title:
                    print("  🎮 DETECTED: Multiplayer mode")
                elif 'Campaign' in title:
                    print("  📖 DETECTED: Campaign mode")
                else:
                    print(f"  ❓ Unknown mode: {title}")
            
        except Exception as e:
            print(f"[DIAG] Game mode check error: {e}")
    
    def read_int(self, address):
        """Read integer from memory"""
        try:
            buffer = ctypes.create_string_buffer(4)
            bytes_read = ctypes.c_size_t(0)
            
            result = ctypes.windll.kernel32.ReadProcessMemory(
                self.process_handle, ctypes.c_void_p(address),
                buffer, 4, ctypes.byref(bytes_read)
            )
            
            if result and bytes_read.value == 4:
                return struct.unpack('<i', buffer.raw)[0]
        except Exception:
            pass
        return 0
    
    def read_float(self, address):
        """Read float from memory"""
        try:
            buffer = ctypes.create_string_buffer(4)
            bytes_read = ctypes.c_size_t(0)
            
            result = ctypes.windll.kernel32.ReadProcessMemory(
                self.process_handle, ctypes.c_void_p(address),
                buffer, 4, ctypes.byref(bytes_read)
            )
            
            if result and bytes_read.value == 4:
                return struct.unpack('<f', buffer.raw)[0]
        except Exception:
            pass
        return 0.0
    
    def run_full_diagnostics(self):
        """Run complete diagnostic suite"""
        print("=" * 60)
        print("COD WWII MEMORY DIAGNOSTICS")
        print("=" * 60)
        
        # Step 1: Find process
        if not self.find_cod_process():
            return False
        
        # Step 2: Test access
        if not self.get_process_access():
            return False
        
        # Step 3: Find base address
        if not self.find_base_address():
            return False
        
        # Step 4: Test memory reading
        if not self.test_memory_reading():
            return False
        
        # Step 5: Check game mode
        self.check_game_mode()
        
        # Step 6: Scan for patterns
        self.scan_for_health_pattern()
        
        print("\n" + "=" * 60)
        print("DIAGNOSTIC COMPLETE")
        print("=" * 60)
        
        return True

if __name__ == "__main__":
    diag = MemoryDiagnostics()
    diag.run_full_diagnostics()
