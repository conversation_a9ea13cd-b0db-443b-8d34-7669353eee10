#!/usr/bin/env python3
"""
Find REAL Base Address Scanner
==============================

Scan for the ACTUAL base address of COD WWII.
Your offsets might be correct but base address is wrong.

Author: Enhanced System
Version: 1.0
"""

import psutil
import ctypes
import struct

def find_real_base_address():
    """Find the REAL base address of COD WWII"""
    print("[SCANNER] Finding REAL COD WWII base address...")
    
    # Find process
    cod_pid = None
    for proc in psutil.process_iter(['pid', 'name']):
        if 's2_mp64_ship.exe' in proc.info['name']:
            cod_pid = proc.info['pid']
            break
    
    if not cod_pid:
        print("[SCANNER] COD WWII not found!")
        return
    
    # Get process handle
    handle = ctypes.windll.kernel32.OpenProcess(0x1F0FFF, False, cod_pid)
    if not handle:
        print("[SCANNER] Failed to get process handle")
        return
    
    print(f"[SCANNER] Testing COD WWII PID {cod_pid}")
    
    # Test multiple possible base addresses
    possible_bases = [
        0x140000000,  # Your current guess
        0x400000,     # Classic 32-bit base
        0x10000000,   # Alternative base
        0x100000000,  # 64-bit alternative
        0x180000000,  # Higher 64-bit base
        0x7FF000000,  # Windows 10 64-bit common
    ]
    
    player_base_offset = 0x15F4A80  # Your player base offset
    
    for base_addr in possible_bases:
        print(f"\n[SCANNER] Testing base address: 0x{base_addr:X}")
        
        try:
            # Test player base pointer
            player_base_addr = base_addr + player_base_offset
            player_base_ptr = read_int(handle, player_base_addr)
            
            print(f"  Player base at 0x{player_base_addr:X}: 0x{player_base_ptr:X}")
            
            if player_base_ptr != 0:
                print(f"  SUCCESS! Non-zero player base pointer found!")
                
                # Test health reading
                if 0x10000000 <= player_base_ptr <= 0x7FFFFFFF0000:  # Valid pointer range
                    health_addr = player_base_ptr + 0xF8  # Your health offset
                    health = read_int(handle, health_addr)
                    
                    print(f"  Health at 0x{health_addr:X}: {health}")
                    
                    if 1 <= health <= 300:  # Reasonable health for zombies
                        print(f"  JACKPOT! Base 0x{base_addr:X} with health {health}")
                        print(f"  Use this base address: 0x{base_addr:X}")
                        return base_addr
                else:
                    print(f"  Invalid pointer range: 0x{player_base_ptr:X}")
            else:
                print(f"  Null pointer - wrong base address")
                
        except Exception as e:
            print(f"  Error testing 0x{base_addr:X}: {e}")
    
    print("\n[SCANNER] No valid base address found!")
    print("[SCANNER] Make sure you're IN an active Nazi Zombies match!")
    
    ctypes.windll.kernel32.CloseHandle(handle)

def read_int(handle, address):
    """Read 32-bit integer from memory"""
    try:
        buffer = ctypes.create_string_buffer(4)
        bytes_read = ctypes.c_size_t(0)
        result = ctypes.windll.kernel32.ReadProcessMemory(
            handle, ctypes.c_void_p(address),
            buffer, 4, ctypes.byref(bytes_read))
        if result and bytes_read.value == 4:
            return struct.unpack('<i', buffer.raw)[0]
    except Exception:
        pass
    return 0

def scan_for_health_values():
    """Scan memory for your current health value"""
    print("\n[HEALTH SCANNER] Scanning for health values...")
    print("[HEALTH SCANNER] Make sure you're IN Nazi Zombies with visible health!")
    
    # Find process
    cod_pid = None
    for proc in psutil.process_iter(['pid', 'name']):
        if 's2_mp64_ship.exe' in proc.info['name']:
            cod_pid = proc.info['pid']
            break
    
    if not cod_pid:
        return
    
    handle = ctypes.windll.kernel32.OpenProcess(0x1F0FFF, False, cod_pid)
    if not handle:
        return
    
    print(f"[HEALTH SCANNER] What's your CURRENT health in zombies? (e.g., 100, 75, 50)")
    print("[HEALTH SCANNER] I'll scan for that value...")
    
    # Scan common health values
    health_values_to_scan = [100, 150, 200, 75, 50, 25]
    
    for health_val in health_values_to_scan:
        print(f"\n[HEALTH SCANNER] Scanning for health value: {health_val}")
        
        # Scan memory regions for this health value
        base_addresses = [0x140000000, 0x400000, 0x10000000]
        
        for base in base_addresses:
            found_count = 0
            for offset in range(0, 0x1000000, 0x1000):  # Scan 16MB in 4KB chunks
                try:
                    addr = base + offset
                    value = read_int(handle, addr)
                    
                    if value == health_val:
                        print(f"  Found {health_val} at 0x{addr:X}")
                        found_count += 1
                        
                        if found_count >= 5:  # Stop after finding 5 instances
                            break
                            
                except Exception:
                    continue
            
            if found_count > 0:
                print(f"  Found {found_count} instances of {health_val} with base 0x{base:X}")
    
    ctypes.windll.kernel32.CloseHandle(handle)

if __name__ == "__main__":
    print("=" * 60)
    print("COD WWII BASE ADDRESS SCANNER")  
    print("=" * 60)
    print("")
    print("IMPORTANT: Start Nazi Zombies match FIRST!")
    print("Make sure you're actually PLAYING, not in menu!")
    print("")
    
    # Find real base address
    real_base = find_real_base_address()
    
    if not real_base:
        # If base address scanning failed, try health scanning
        scan_for_health_values()
    
    print("\n" + "=" * 60)
    print("SCANNER COMPLETE")
    print("=" * 60)



