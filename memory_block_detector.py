#!/usr/bin/env python3
"""
Memory Block Detector
=====================

Detects what's blocking memory access to COD WWII.
Checks for anti-cheat, DEP, ASLR, and other protection mechanisms.

Author: Enhanced System
Version: 1.0
"""

import ctypes
import ctypes.wintypes
import psutil
import os
import sys
import subprocess
import winreg

class MemoryBlockDetector:
    """Detect memory protection mechanisms"""
    
    def __init__(self):
        self.process_id = None
        self.process_handle = None
        self.process_name = "s2_mp64_ship.exe"
        
    def find_cod_process(self):
        """Find COD WWII process"""
        for proc in psutil.process_iter(['pid', 'name', 'exe']):
            if self.process_name in proc.info['name']:
                self.process_id = proc.info['pid']
                print(f"✅ Found {self.process_name}: PID {self.process_id}")
                return True
        
        print(f"❌ {self.process_name} not running!")
        return False
    
    def check_admin_privileges(self):
        """Check if running as administrator"""
        try:
            is_admin = ctypes.windll.shell32.IsUserAnAdmin()
            if is_admin:
                print("✅ Running as Administrator")
                return True
            else:
                print("❌ NOT running as Administrator")
                print("   → Try running as Administrator for better access")
                return False
        except:
            print("❓ Cannot determine admin status")
            return False
    
    def check_process_access_rights(self):
        """Test different process access levels"""
        print("\n🔍 Testing Process Access Rights:")
        
        access_tests = [
            (0x1F0FFF, "PROCESS_ALL_ACCESS"),
            (0x0400, "PROCESS_QUERY_INFORMATION"),
            (0x0010, "PROCESS_VM_READ"),
            (0x0020, "PROCESS_VM_WRITE"),
            (0x0030, "PROCESS_VM_READ | PROCESS_VM_WRITE"),
            (0x0008, "PROCESS_VM_OPERATION"),
            (0x0038, "PROCESS_VM_READ | PROCESS_VM_WRITE | PROCESS_VM_OPERATION")
        ]
        
        successful_access = []
        
        for access_flag, name in access_tests:
            try:
                handle = ctypes.windll.kernel32.OpenProcess(access_flag, False, self.process_id)
                if handle:
                    print(f"   ✅ {name}")
                    successful_access.append(name)
                    if access_flag == 0x1F0FFF:
                        self.process_handle = handle
                    else:
                        ctypes.windll.kernel32.CloseHandle(handle)
                else:
                    error = ctypes.windll.kernel32.GetLastError()
                    print(f"   ❌ {name} - Error: {error}")
            except Exception as e:
                print(f"   ❌ {name} - Exception: {e}")
        
        return len(successful_access) > 0
    
    def check_dep_protection(self):
        """Check Data Execution Prevention"""
        print("\n🛡️  Checking DEP (Data Execution Prevention):")
        
        try:
            # Check system DEP policy
            result = subprocess.run(['bcdedit', '/enum'], capture_output=True, text=True, shell=True)
            if 'nx' in result.stdout.lower():
                if 'optout' in result.stdout.lower():
                    print("   ⚠️  DEP enabled for all programs except opted-out")
                elif 'optin' in result.stdout.lower():
                    print("   ✅ DEP enabled only for Windows programs")
                else:
                    print("   ❌ DEP fully enabled (may block memory access)")
            else:
                print("   ✅ DEP not detected or disabled")
                
        except Exception as e:
            print(f"   ❓ Cannot check DEP: {e}")
    
    def check_aslr_protection(self):
        """Check Address Space Layout Randomization"""
        print("\n🎲 Checking ASLR (Address Space Layout Randomization):")
        
        try:
            process = psutil.Process(self.process_id)
            memory_maps = process.memory_maps()
            
            # Check if base addresses are randomized
            base_addresses = []
            for mmap in memory_maps[:5]:  # Check first 5 mappings
                if hasattr(mmap, 'addr'):
                    addr = mmap.addr.split('-')[0]
                    base_addresses.append(addr)
                elif hasattr(mmap, 'path') and mmap.path:
                    # Try to extract address from string representation
                    addr_str = str(mmap).split()[1] if len(str(mmap).split()) > 1 else "unknown"
                    base_addresses.append(addr_str)
            
            print(f"   Base addresses: {base_addresses[:3]}...")
            
            # Check if addresses look randomized (high entropy)
            if any(addr.startswith('0x7FF') or addr.startswith('7FF') for addr in base_addresses):
                print("   ❌ ASLR is ACTIVE (addresses randomized)")
                print("   → This explains why hardcoded addresses fail!")
            else:
                print("   ✅ ASLR appears disabled or low entropy")
                
        except Exception as e:
            print(f"   ❓ Cannot check ASLR: {e}")
    
    def check_anticheat_processes(self):
        """Check for anti-cheat processes"""
        print("\n🚫 Checking for Anti-Cheat Systems:")
        
        anticheat_processes = [
            'EasyAntiCheat.exe',
            'BEService.exe',  # BattlEye
            'vgc.exe',        # Vanguard
            'faceit.exe',
            'ESEA.exe',
            'steam.exe'       # Steam VAC
        ]
        
        running_anticheats = []
        
        for proc in psutil.process_iter(['name']):
            proc_name = proc.info['name'].lower()
            for anticheat in anticheat_processes:
                if anticheat.lower() in proc_name:
                    running_anticheats.append(proc.info['name'])
        
        if running_anticheats:
            print(f"   ❌ Anti-cheat detected: {running_anticheats}")
            print("   → These may block memory access!")
        else:
            print("   ✅ No obvious anti-cheat processes detected")
    
    def test_basic_memory_read(self):
        """Test basic memory reading capability"""
        print("\n📖 Testing Basic Memory Reading:")
        
        if not self.process_handle:
            print("   ❌ No process handle available")
            return False
        
        try:
            # Try to read from a safe location (process base)
            process = psutil.Process(self.process_id)
            
            # Get a memory region to test
            memory_maps = process.memory_maps()
            if memory_maps:
                # Try to parse address from first mapping
                first_map = memory_maps[0]
                
                if hasattr(first_map, 'addr'):
                    addr_str = first_map.addr.split('-')[0]
                else:
                    # Fallback: try to extract from string representation
                    map_str = str(first_map)
                    parts = map_str.split()
                    addr_str = parts[1] if len(parts) > 1 else "0x140000000"
                
                test_addr = int(addr_str, 16) if addr_str.startswith('0x') else int(addr_str, 16)
                
                print(f"   Testing read from: 0x{test_addr:X}")
                
                # Try to read 4 bytes
                buffer = ctypes.create_string_buffer(4)
                bytes_read = ctypes.c_size_t(0)
                
                result = ctypes.windll.kernel32.ReadProcessMemory(
                    self.process_handle, ctypes.c_void_p(test_addr),
                    buffer, 4, ctypes.byref(bytes_read)
                )
                
                if result and bytes_read.value == 4:
                    value = int.from_bytes(buffer.raw, byteorder='little', signed=True)
                    print(f"   ✅ Successfully read: {value} (0x{value & 0xFFFFFFFF:X})")
                    return True
                else:
                    error = ctypes.windll.kernel32.GetLastError()
                    print(f"   ❌ Read failed - Error: {error}")
                    return False
            else:
                print("   ❌ No memory maps available")
                return False
                
        except Exception as e:
            print(f"   ❌ Memory read test failed: {e}")
            return False
    
    def check_windows_defender(self):
        """Check Windows Defender real-time protection"""
        print("\n🛡️  Checking Windows Defender:")
        
        try:
            result = subprocess.run([
                'powershell', '-Command', 
                'Get-MpPreference | Select-Object DisableRealtimeMonitoring'
            ], capture_output=True, text=True, shell=True)
            
            if 'False' in result.stdout:
                print("   ❌ Windows Defender Real-time Protection is ON")
                print("   → May interfere with memory access")
            elif 'True' in result.stdout:
                print("   ✅ Windows Defender Real-time Protection is OFF")
            else:
                print("   ❓ Cannot determine Defender status")
                
        except Exception as e:
            print(f"   ❓ Cannot check Windows Defender: {e}")
    
    def suggest_bypasses(self):
        """Suggest ways to bypass memory blocks"""
        print("\n💡 BYPASS SUGGESTIONS:")
        print("=" * 50)
        
        print("1. 🔧 IMMEDIATE FIXES:")
        print("   • Run this program as Administrator")
        print("   • Temporarily disable Windows Defender")
        print("   • Close unnecessary anti-cheat software")
        
        print("\n2. 🎯 TECHNICAL BYPASSES:")
        print("   • Use DLL injection instead of direct memory access")
        print("   • Hook Windows API functions (SetWindowsHookEx)")
        print("   • Use kernel-mode driver for unrestricted access")
        print("   • Memory mapping with PAGE_EXECUTE_READWRITE")
        
        print("\n3. 🔄 ALTERNATIVE APPROACHES:")
        print("   • Input simulation instead of memory modification")
        print("   • Screen reading + automated input")
        print("   • Game overlay with visual assistance")
        print("   • Network packet modification")
        
        print("\n4. 🛠️  TOOLS TO TRY:")
        print("   • Cheat Engine (has built-in bypass methods)")
        print("   • Process Hacker (advanced process manipulation)")
        print("   • WinAPIOverride (API hooking)")
        print("   • Detours library (Microsoft's hooking library)")

def main():
    """Main detection routine"""
    print("=" * 60)
    print("COD WWII MEMORY BLOCK DETECTOR")
    print("=" * 60)
    print()
    
    detector = MemoryBlockDetector()
    
    # Step 1: Find process
    if not detector.find_cod_process():
        print("\n❌ Cannot proceed without COD WWII running")
        return
    
    # Step 2: Check admin privileges
    detector.check_admin_privileges()
    
    # Step 3: Test process access
    detector.check_process_access_rights()
    
    # Step 4: Check protection mechanisms
    detector.check_dep_protection()
    detector.check_aslr_protection()
    detector.check_anticheat_processes()
    detector.check_windows_defender()
    
    # Step 5: Test actual memory reading
    detector.test_basic_memory_read()
    
    # Step 6: Provide solutions
    detector.suggest_bypasses()
    
    print("\n" + "=" * 60)
    print("DETECTION COMPLETE")
    print("=" * 60)

if __name__ == "__main__":
    main()
