#!/usr/bin/env python3
"""
SAFE Memory Hacks for COD WWII
==============================

Safer approach that won't crash the game.
Uses careful memory validation and selective targeting.

Author: Enhanced System
Version: SAFE 1.0
"""

import ctypes
import ctypes.wintypes
import psutil
import time
import threading
import tkinter as tk
from tkinter import ttk, scrolledtext
import struct

class SafeCODHacks:
    """Safe COD WWII hack system that won't crash the game"""
    
    def __init__(self):
        self.process_id = None
        self.process_handle = None
        self.base_address = None
        self.safe_addresses = []
        self.original_values = {}
        
        self.features = {
            'godmode': False,
            'infinite_ammo': False,
            'no_recoil': False
        }
        
        self.status_callback = None
        
    def set_status_callback(self, callback):
        """Set callback for status updates"""
        self.status_callback = callback
    
    def log(self, message):
        """Log message with callback"""
        print(message)
        if self.status_callback:
            self.status_callback(message)
    
    def initialize(self):
        """Initialize the hack system safely"""
        self.log("[SAFE] Starting SAFE COD WWII hack system...")
        
        if not self.find_process():
            return False
        
        if not self.get_handle():
            return False
        
        if not self.find_base():
            return False
        
        if not self.find_safe_addresses():
            return False
        
        self.log("[SAFE] ✅ System ready - SAFE MODE!")
        return True
    
    def find_process(self):
        """Find COD WWII process"""
        for proc in psutil.process_iter(['pid', 'name']):
            if 's2_mp64_ship.exe' in proc.info['name']:
                self.process_id = proc.info['pid']
                self.log(f"[SAFE] ✅ Found COD WWII: PID {self.process_id}")
                return True
        
        self.log("[SAFE] ❌ COD WWII not found!")
        return False
    
    def get_handle(self):
        """Get process handle"""
        try:
            # Use minimal required permissions
            self.process_handle = ctypes.windll.kernel32.OpenProcess(
                0x0038, False, self.process_id)  # VM_READ | VM_WRITE | VM_OPERATION
            
            if self.process_handle:
                self.log("[SAFE] ✅ Process handle acquired (minimal permissions)")
                return True
            else:
                self.log("[SAFE] ❌ Failed to get handle")
                return False
        except Exception as e:
            self.log(f"[SAFE] ❌ Handle error: {e}")
            return False
    
    def find_base(self):
        """Find base address safely"""
        try:
            hModules = (ctypes.wintypes.HMODULE * 1024)()
            cbNeeded = ctypes.wintypes.DWORD()
            
            result = ctypes.windll.psapi.EnumProcessModules(
                self.process_handle, ctypes.byref(hModules), 
                ctypes.sizeof(hModules), ctypes.byref(cbNeeded)
            )
            
            if result:
                self.base_address = hModules[0]
                self.log(f"[SAFE] ✅ Base address: 0x{self.base_address:X}")
                return True
            else:
                self.log("[SAFE] ❌ Failed to get base address")
                return False
                
        except Exception as e:
            self.log(f"[SAFE] ❌ Base error: {e}")
            return False
    
    def is_safe_to_write(self, address, value):
        """Check if it's safe to write to an address"""
        try:
            # Read current value
            current = self.read_int(address)
            
            # Don't write to critical system values
            if current == 0 and value != 0:
                return False  # Don't change NULL pointers
            
            # Don't write extremely large values that could cause overflow
            if abs(value) > 100000:
                return False
            
            # Don't write to addresses that look like pointers
            if 0x10000 < current < 0x7FFFFFFFFFFF:
                return False
            
            # Only write to reasonable game values
            if not (0 <= current <= 10000):
                return False
            
            return True
            
        except:
            return False
    
    def read_int(self, address):
        """Safely read integer from memory"""
        try:
            buffer = ctypes.create_string_buffer(4)
            bytes_read = ctypes.c_size_t(0)
            
            result = ctypes.windll.kernel32.ReadProcessMemory(
                self.process_handle, ctypes.c_void_p(address),
                buffer, 4, ctypes.byref(bytes_read)
            )
            
            if result and bytes_read.value == 4:
                return struct.unpack('<i', buffer.raw)[0]
            return 0
        except:
            return 0
    
    def write_int(self, address, value):
        """Safely write integer to memory"""
        try:
            data = struct.pack('<i', value)
            bytes_written = ctypes.c_size_t(0)
            
            result = ctypes.windll.kernel32.WriteProcessMemory(
                self.process_handle, ctypes.c_void_p(address),
                data, len(data), ctypes.byref(bytes_written)
            )
            
            return result and bytes_written.value == len(data)
        except:
            return False
    
    def find_safe_addresses(self):
        """Find safe addresses to modify"""
        self.log("[SAFE] Scanning for SAFE addresses only...")
        
        candidates = []
        
        # Scan smaller, safer ranges
        scan_start = self.base_address + 0x1000000  # Skip first 16MB (critical code)
        scan_end = self.base_address + 0x3000000    # Only scan 32MB total
        
        for addr in range(scan_start, scan_end, 0x10000):  # 64KB steps (much safer)
            try:
                # Read a small chunk
                buffer = ctypes.create_string_buffer(0x1000)  # 4KB
                bytes_read = ctypes.c_size_t(0)
                
                result = ctypes.windll.kernel32.ReadProcessMemory(
                    self.process_handle, ctypes.c_void_p(addr),
                    buffer, 0x1000, ctypes.byref(bytes_read)
                )
                
                if result and bytes_read.value > 0:
                    data = buffer.raw[:bytes_read.value]
                    
                    # Look for safe game values only
                    for i in range(0, len(data) - 4, 16):  # Skip more bytes
                        value = struct.unpack('<i', data[i:i+4])[0]
                        
                        # Only target values that look like health/ammo (1-200)
                        if 1 <= value <= 200:
                            candidate_addr = addr + i
                            candidates.append((candidate_addr, value))
                            
                            if len(candidates) >= 50:  # Much fewer addresses
                                break
                    
                    if len(candidates) >= 50:
                        break
                        
            except:
                continue
        
        # Test which ones are safe to write to
        self.log("[SAFE] Testing addresses for safety...")
        
        for addr, original in candidates:
            if self.is_safe_to_write(addr, 999):
                # Test write and restore
                if self.write_int(addr, 777):
                    time.sleep(0.01)  # Small delay
                    
                    # Verify write worked
                    if self.read_int(addr) == 777:
                        # Restore original immediately
                        self.write_int(addr, original)
                        
                        # Store as safe address
                        self.safe_addresses.append(addr)
                        self.original_values[addr] = original
                        
                        if len(self.safe_addresses) >= 10:  # Only use 10 addresses
                            break
        
        self.log(f"[SAFE] ✅ Found {len(self.safe_addresses)} SAFE addresses")
        return len(self.safe_addresses) > 0
    
    def start_safe_godmode(self):
        """Start safe godmode that won't crash"""
        if self.features['godmode']:
            return
        
        self.features['godmode'] = True
        self.log("[GODMODE] 🛡️  Starting SAFE godmode...")
        
        def safe_godmode_worker():
            while self.features['godmode']:
                try:
                    # Only modify a few addresses at a time
                    for i, addr in enumerate(self.safe_addresses[:5]):  # Only first 5
                        current = self.read_int(addr)
                        
                        # Only modify if value looks like health and is low
                        if 1 <= current <= 150:
                            self.write_int(addr, 200)  # Reasonable health value
                    
                    time.sleep(0.5)  # Much slower updates
                    
                except Exception as e:
                    self.log(f"[GODMODE] Error: {e}")
                    time.sleep(2.0)
        
        threading.Thread(target=safe_godmode_worker, daemon=True).start()
        self.log("[GODMODE] ✅ SAFE godmode active!")
    
    def start_safe_infinite_ammo(self):
        """Start safe infinite ammo"""
        if self.features['infinite_ammo']:
            return
        
        self.features['infinite_ammo'] = True
        self.log("[AMMO] 🔫 Starting SAFE infinite ammo...")
        
        def safe_ammo_worker():
            while self.features['infinite_ammo']:
                try:
                    # Only modify addresses that could be ammo
                    for addr in self.safe_addresses[5:]:  # Use different addresses
                        current = self.read_int(addr)
                        
                        # Only modify if value looks like ammo count
                        if 0 <= current <= 100:
                            self.write_int(addr, 99)  # Reasonable ammo value
                    
                    time.sleep(1.0)  # Very slow updates
                    
                except Exception as e:
                    self.log(f"[AMMO] Error: {e}")
                    time.sleep(2.0)
        
        threading.Thread(target=safe_ammo_worker, daemon=True).start()
        self.log("[AMMO] ✅ SAFE infinite ammo active!")
    
    def restore_original_values(self):
        """Restore all original values to prevent crashes"""
        self.log("[SAFE] Restoring original values...")
        
        for addr, original in self.original_values.items():
            try:
                self.write_int(addr, original)
            except:
                continue
        
        self.log("[SAFE] ✅ Original values restored")
    
    def stop_feature(self, feature_name):
        """Stop a specific feature safely"""
        if feature_name in self.features:
            self.features[feature_name] = False
            self.log(f"[SAFE] {feature_name.upper()} stopped safely")
    
    def stop_all(self):
        """Stop all features and restore values"""
        for feature in self.features:
            self.features[feature] = False
        
        # Restore original values to prevent issues
        self.restore_original_values()
        self.log("[SAFE] All features stopped - values restored")

class SafeGUI:
    """Safe GUI for the hack system"""
    
    def __init__(self):
        self.hacks = SafeCODHacks()
        self.root = tk.Tk()
        self.root.title("SAFE COD WWII Hacks - No Crash")
        self.root.geometry("500x400")
        self.root.configure(bg='#1a1a1a')
        
        # Handle window close
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        self.create_interface()
        self.hacks.set_status_callback(self.add_log)
    
    def create_interface(self):
        """Create the safe GUI"""
        # Title
        title = tk.Label(self.root, text="🛡️  SAFE COD WWII HACKS", 
                        font=('Arial', 16, 'bold'), fg='green', bg='#1a1a1a')
        title.pack(pady=10)
        
        # Subtitle
        subtitle = tk.Label(self.root, text="Safe Mode - Won't Crash Game!", 
                           font=('Arial', 10), fg='lime', bg='#1a1a1a')
        subtitle.pack(pady=5)
        
        # Initialize button
        self.init_btn = tk.Button(self.root, text="🚀 INITIALIZE SAFE SYSTEM", 
                                 bg='darkgreen', fg='white', font=('Arial', 12, 'bold'),
                                 command=self.initialize_system, width=25)
        self.init_btn.pack(pady=10)
        
        # Feature buttons
        features_frame = tk.Frame(self.root, bg='#1a1a1a')
        features_frame.pack(pady=10)
        
        self.feature_buttons = {}
        
        features = [
            ("🛡️  SAFE GODMODE", 'godmode', 'darkgreen'),
            ("🔫 SAFE INFINITE AMMO", 'infinite_ammo', 'darkorange'),
        ]
        
        for text, feature, color in features:
            btn = tk.Button(features_frame, text=text, bg=color, fg='white',
                           font=('Arial', 11, 'bold'), width=25,
                           command=lambda f=feature: self.toggle_feature(f),
                           state='disabled')
            btn.pack(pady=5)
            self.feature_buttons[feature] = btn
        
        # Control buttons
        control_frame = tk.Frame(self.root, bg='#1a1a1a')
        control_frame.pack(pady=10)
        
        tk.Button(control_frame, text="⏹️  STOP ALL & RESTORE", bg='darkred', fg='white',
                 font=('Arial', 10, 'bold'), command=self.stop_all).pack(side=tk.LEFT, padx=5)
        
        # Status log
        log_frame = tk.LabelFrame(self.root, text="Status Log", fg='cyan', bg='#1a1a1a')
        log_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=8, bg='black', fg='lime',
                                                 font=('Consolas', 9))
        self.log_text.pack(fill='both', expand=True, padx=5, pady=5)
        
        # Safety instructions
        safety_frame = tk.LabelFrame(self.root, text="Safety Instructions", fg='yellow', bg='#1a1a1a')
        safety_frame.pack(fill='x', padx=10, pady=5)
        
        instructions = [
            "• This version uses SAFE memory modification",
            "• Only modifies 5-10 addresses carefully",
            "• Restores original values when stopped",
            "• Much less likely to crash the game",
            "• Always click 'STOP ALL' before closing"
        ]
        
        for inst in instructions:
            tk.Label(safety_frame, text=inst, fg='white', bg='#1a1a1a',
                    font=('Arial', 8)).pack(anchor='w', padx=5)
    
    def add_log(self, message):
        """Add message to log"""
        self.log_text.insert(tk.END, message + '\n')
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def initialize_system(self):
        """Initialize the safe hack system"""
        self.init_btn.config(state='disabled', text='INITIALIZING...')
        
        def init_worker():
            success = self.hacks.initialize()
            
            if success:
                self.init_btn.config(bg='green', text='✅ SAFE SYSTEM READY', state='normal')
                for btn in self.feature_buttons.values():
                    btn.config(state='normal')
            else:
                self.init_btn.config(bg='red', text='❌ INIT FAILED', state='normal')
        
        threading.Thread(target=init_worker, daemon=True).start()
    
    def toggle_feature(self, feature_name):
        """Toggle a feature on/off"""
        if self.hacks.features[feature_name]:
            # Stop feature
            self.hacks.stop_feature(feature_name)
            self.feature_buttons[feature_name].config(bg='gray', text=f"▶️  START SAFE {feature_name.upper()}")
        else:
            # Start feature
            if feature_name == 'godmode':
                self.hacks.start_safe_godmode()
            elif feature_name == 'infinite_ammo':
                self.hacks.start_safe_infinite_ammo()
            
            self.feature_buttons[feature_name].config(bg='green', text=f"⏹️  STOP {feature_name.upper()}")
    
    def stop_all(self):
        """Stop all features safely"""
        self.hacks.stop_all()
        for feature, btn in self.feature_buttons.items():
            btn.config(bg='gray', text=f"▶️  START SAFE {feature.upper()}")
    
    def on_closing(self):
        """Handle window closing safely"""
        self.add_log("[SAFE] Shutting down safely...")
        self.hacks.stop_all()  # This restores original values
        time.sleep(0.5)  # Give time for restoration
        self.root.destroy()
    
    def run(self):
        """Run the safe GUI"""
        self.add_log("🛡️  SAFE COD WWII HACKS - Ready!")
        self.add_log("This version won't crash your game")
        self.add_log("Click INITIALIZE SAFE SYSTEM to begin")
        self.root.mainloop()

if __name__ == "__main__":
    print("=" * 60)
    print("SAFE COD WWII HACKS - No Crash Version")
    print("=" * 60)
    print("Starting safe GUI...")
    
    app = SafeGUI()
    app.run()
