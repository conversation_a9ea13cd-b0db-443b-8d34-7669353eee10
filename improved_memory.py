#!/usr/bin/env python3
"""
Improved Memory Access System
=============================

Uses CORRECT July 2025 Nazi Zombies offsets with better error handling.
Combines pymem and ctypes for maximum compatibility.

Author: Enhanced System
Version: 2.0
"""

import pymem
import pymem.process
import ctypes
import ctypes.wintypes
import psutil
import threading
import time
import struct
from fixed_gamepad import FixedGamepadHandler, WindowFocusManager

class ImprovedCODMemory:
    """Improved memory access with CORRECT offsets"""
    
    def __init__(self):
        self.pm = None
        self.process_handle = None
        self.base_address = None
        self.gamepad = FixedGamepadHandler()
        self.focus_manager = WindowFocusManager()
        
        # CORRECT July 2025 Nazi Zombies offsets
        self.offsets = {
            # Health system
            'health_base': 0xA2D7DC8,
            'health_offset': 0x2DC,
            
            # Player info
            'username_base': 0x0E4650B0,
            'username_offset': 0x144,
            'position_base': 0xA0C7388,
            'special_ability': 0xA4B1888,
            
            # Weapons (all relative to position_base)
            'lethals': 0x768,
            'weapon1': 0x784,
            'weapon2': 0x780,
            'm1_garand': 0x7C8,
            'mp1': 0x7B4,
            'mp2': 0x7B0,
            'm30_drilling': 0x7B0,
            'type100': 0x7C8,
            'svt40': 0x7B0,
            'm1928': 0x7C8,
            
            # Current weapon
            'current_ammo_base': 0x10948C8,
            'current_ammo_offset': 0x8,
            'current_weapon': 0x97863808,
            
            # Entity system
            'entity_list': 0xA0C7130,
            'entity_size': 0x418,
            'max_entities': 32,
            
            # Entity offsets
            'entity_clientnum': 0x0,
            'entity_weapon_id': 0x94,
            'entity_origin': 0x21C,
            'entity_head': 0x234,
            'entity_health': 0x2DC,
            
            # Special
            'zombie_array': 0xA0D35B0,
            'refdef': 0x8CE9968,
        }
        
        self.features_active = {
            'godmode': False,
            'infinite_ammo': False,
            'super_jump': False,
            'rapid_fire': False
        }
        
        print("[MEMORY] Improved COD memory system initialized")
    
    def connect(self):
        """Connect to COD WWII with both pymem and ctypes"""
        try:
            # Try pymem first (easier to use)
            self.pm = pymem.Pymem("s2_mp64_ship.exe")
            self.base_address = self.pm.base_address
            print(f"[MEMORY] ✅ Connected via pymem - Base: 0x{self.base_address:X}")
            
            # Also get ctypes handle for backup
            for proc in psutil.process_iter(['pid', 'name']):
                if proc.info['name'] == 's2_mp64_ship.exe':
                    self.process_handle = ctypes.windll.kernel32.OpenProcess(
                        0x1F0FFF, False, proc.info['pid'])
                    break
            
            return True
            
        except Exception as e:
            print(f"[MEMORY] ❌ Connection failed: {e}")
            return False
    
    def test_memory_access(self):
        """Test memory access with CORRECT offsets"""
        if not self.pm:
            return False
        
        try:
            print("[MEMORY] Testing with CORRECT July 2025 offsets...")
            
            # Test health reading
            health_ptr = self.pm.read_int(self.base_address + self.offsets['health_base'])
            print(f"[MEMORY] Health pointer: 0x{health_ptr:X}")
            
            if health_ptr > 0x10000:
                health = self.pm.read_int(health_ptr + self.offsets['health_offset'])
                print(f"[MEMORY] Health value: {health}")
                
                if health > 0:
                    print("[MEMORY] ✅ Health reading WORKS!")
                    
                    # Test entity list
                    entity_base = self.base_address + self.offsets['entity_list']
                    first_entity = self.pm.read_int(entity_base)
                    print(f"[MEMORY] Entity list: 0x{first_entity:X}")
                    
                    # Test current ammo
                    ammo_ptr = self.pm.read_int(self.base_address + self.offsets['current_ammo_base'])
                    if ammo_ptr > 0x10000:
                        ammo = self.pm.read_int(ammo_ptr + self.offsets['current_ammo_offset'])
                        print(f"[MEMORY] Current ammo: {ammo}")
                    
                    return True
                else:
                    print("[MEMORY] Health is 0 - might not be in active match")
            else:
                print("[MEMORY] Invalid health pointer - check if in Nazi Zombies match")
            
            return False
            
        except Exception as e:
            print(f"[MEMORY] Test failed: {e}")
            return False
    
    def start_godmode(self):
        """Start godmode with CORRECT offsets"""
        if self.features_active['godmode']:
            return
        
        self.features_active['godmode'] = True
        print("[GODMODE] Starting with CORRECT offsets")
        
        def godmode_worker():
            while self.features_active['godmode']:
                try:
                    health_ptr = self.pm.read_int(self.base_address + self.offsets['health_base'])
                    
                    if health_ptr > 0x10000:
                        health_addr = health_ptr + self.offsets['health_offset']
                        current_health = self.pm.read_int(health_addr)
                        
                        if 0 < current_health < 200:
                            self.pm.write_int(health_addr, 999)
                            print(f"[GODMODE] Health: {current_health} → 999")
                    
                    time.sleep(0.1)
                    
                except Exception as e:
                    print(f"[GODMODE] Error: {e}")
                    time.sleep(1.0)
        
        threading.Thread(target=godmode_worker, daemon=True).start()
    
    def start_infinite_ammo(self):
        """Start infinite ammo with CORRECT offsets"""
        if self.features_active['infinite_ammo']:
            return
        
        self.features_active['infinite_ammo'] = True
        print("[INFINITE AMMO] Starting with CORRECT offsets")
        
        def ammo_worker():
            while self.features_active['infinite_ammo']:
                try:
                    # Set current weapon ammo
                    ammo_ptr = self.pm.read_int(self.base_address + self.offsets['current_ammo_base'])
                    if ammo_ptr > 0x10000:
                        self.pm.write_int(ammo_ptr + self.offsets['current_ammo_offset'], 999)
                    
                    # Set all weapon ammo via position base
                    pos_base = self.base_address + self.offsets['position_base']
                    weapon_offsets = ['weapon1', 'weapon2', 'm1_garand', 'type100', 'm1928']
                    
                    for weapon in weapon_offsets:
                        try:
                            weapon_ptr = self.pm.read_int(pos_base + self.offsets[weapon])
                            if weapon_ptr > 0x10000:
                                self.pm.write_int(weapon_ptr, 999)
                        except:
                            continue
                    
                    time.sleep(0.2)
                    
                except Exception as e:
                    print(f"[INFINITE AMMO] Error: {e}")
                    time.sleep(1.0)
        
        threading.Thread(target=ammo_worker, daemon=True).start()
    
    def setup_gamepad_features(self):
        """Setup gamepad controls without focus issues"""
        def on_a_button():
            """Super jump on A button"""
            if self.features_active['super_jump']:
                print("[SUPER JUMP] A button pressed!")
                # Keep COD focused
                self.focus_manager.keep_cod_focused()
                # TODO: Implement position modification for super jump
        
        def on_rt_trigger(value):
            """Rapid fire on RT trigger"""
            if value > 0.3 and self.features_active['rapid_fire']:
                print(f"[RAPID FIRE] RT: {value:.2f}")
                # Prevent window minimize/maximize
                self.focus_manager.prevent_minimize()
                # TODO: Implement weapon fire rate modification
        
        # Register callbacks
        self.gamepad.register_callback('a_button', on_a_button)
        self.gamepad.register_callback('rt_trigger', on_rt_trigger)
        
        # Start monitoring
        return self.gamepad.start_monitoring()
    
    def stop_all_features(self):
        """Stop all active features"""
        for feature in self.features_active:
            self.features_active[feature] = False
        
        self.gamepad.stop_monitoring()
        print("[MEMORY] All features stopped")

# Test the improved system
if __name__ == "__main__":
    memory = ImprovedCODMemory()
    
    if memory.connect():
        print("[TEST] Connection successful!")
        
        if memory.test_memory_access():
            print("[TEST] Memory access working!")
            
            # Setup gamepad
            if memory.setup_gamepad_features():
                print("[TEST] Gamepad setup complete!")
                
                # Enable features for testing
                memory.start_godmode()
                memory.start_infinite_ammo()
                memory.features_active['super_jump'] = True
                memory.features_active['rapid_fire'] = True
                
                print("[TEST] All features active! Test in Nazi Zombies match.")
                print("[TEST] Press Ctrl+C to stop")
                
                try:
                    while True:
                        time.sleep(1)
                except KeyboardInterrupt:
                    memory.stop_all_features()
                    print("[TEST] Stopped")
            else:
                print("[TEST] Gamepad setup failed")
        else:
            print("[TEST] Memory access test failed")
    else:
        print("[TEST] Connection failed")
