# Enhanced Lunar Aimbot Requirements
# ===================================

# Core dependencies
opencv-python>=4.8.0
numpy>=1.21.0
Pillow>=9.0.0

# Input/Output
pyautogui>=0.9.54
pynput>=1.7.6
pygetwindow>=0.0.9

# System monitoring
psutil>=5.9.0

# Windows-specific
pywin32>=304; sys_platform == "win32"

# Neural network and AI
torch>=2.0.0
torchvision>=0.15.0
ultralytics>=8.0.0

# Utilities
termcolor>=2.3.0
colorama>=0.4.6

# Optional: Advanced features
requests>=2.28.0
websockets>=11.0.0

# Development (optional)
pytest>=7.4.0
black>=23.0.0
flake8>=6.0.0

