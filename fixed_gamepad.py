#!/usr/bin/env python3
"""
Fixed Gamepad Handler
====================

Prevents window focus issues when using RT trigger and other gamepad inputs.
Handles gamepad input without interfering with game window focus.

Author: Enhanced System
Version: 1.0
"""

import pygame
import threading
import time
import ctypes
import ctypes.wintypes

class FixedGamepadHandler:
    """Gamepad handler that doesn't steal window focus"""
    
    def __init__(self):
        # Initialize pygame without video to prevent focus issues
        pygame.init()
        pygame.joystick.init()
        
        self.gamepad = None
        self.running = False
        self.callbacks = {}
        
        # Prevent pygame from stealing focus
        import os
        os.environ['SDL_VIDEODRIVER'] = 'dummy'
        
        if pygame.joystick.get_count() > 0:
            self.gamepad = pygame.joystick.Joystick(0)
            self.gamepad.init()
            print(f"[GAMEPAD] Connected: {self.gamepad.get_name()}")
        else:
            print("[GAMEPAD] No gamepad detected")
    
    def start_monitoring(self):
        """Start gamepad monitoring in background thread"""
        if not self.gamepad:
            return False
        
        self.running = True
        threading.Thread(target=self._monitor_loop, daemon=True).start()
        print("[GAMEPAD] Monitoring started (focus-safe)")
        return True
    
    def stop_monitoring(self):
        """Stop gamepad monitoring"""
        self.running = False
        print("[GAMEPAD] Monitoring stopped")
    
    def register_callback(self, input_type, callback):
        """Register callback for specific input
        
        input_type can be:
        - 'a_button'
        - 'rt_trigger' 
        - 'right_stick'
        - etc.
        """
        self.callbacks[input_type] = callback
        print(f"[GAMEPAD] Registered callback for {input_type}")
    
    def _monitor_loop(self):
        """Main monitoring loop - runs in background"""
        last_states = {
            'a_button': False,
            'rt_trigger': 0.0,
            'right_stick_x': 0.0,
            'right_stick_y': 0.0
        }
        
        while self.running:
            try:
                # Pump events but don't process them (prevents focus stealing)
                pygame.event.pump()
                
                # Check A button
                a_pressed = self.gamepad.get_button(0)
                if a_pressed != last_states['a_button']:
                    if 'a_button' in self.callbacks and a_pressed:
                        self.callbacks['a_button']()
                    last_states['a_button'] = a_pressed
                
                # Check RT trigger (axis 5 on Xbox controller)
                rt_value = self.gamepad.get_axis(5)
                if abs(rt_value - last_states['rt_trigger']) > 0.1:
                    if 'rt_trigger' in self.callbacks:
                        self.callbacks['rt_trigger'](rt_value)
                    last_states['rt_trigger'] = rt_value
                
                # Check right stick (axes 2 and 3)
                right_x = self.gamepad.get_axis(2)
                right_y = self.gamepad.get_axis(3)
                
                if (abs(right_x - last_states['right_stick_x']) > 0.1 or 
                    abs(right_y - last_states['right_stick_y']) > 0.1):
                    if 'right_stick' in self.callbacks:
                        self.callbacks['right_stick'](right_x, right_y)
                    last_states['right_stick_x'] = right_x
                    last_states['right_stick_y'] = right_y
                
                time.sleep(0.01)  # 100 FPS polling
                
            except Exception as e:
                print(f"[GAMEPAD] Error: {e}")
                time.sleep(0.1)

class WindowFocusManager:
    """Manages window focus to prevent game window issues"""
    
    def __init__(self):
        self.cod_window = None
        self.find_cod_window()
    
    def find_cod_window(self):
        """Find COD WWII window handle"""
        try:
            def enum_windows_callback(hwnd, windows):
                if ctypes.windll.user32.IsWindowVisible(hwnd):
                    length = ctypes.windll.user32.GetWindowTextLengthW(hwnd)
                    if length > 0:
                        buffer = ctypes.create_unicode_buffer(length + 1)
                        ctypes.windll.user32.GetWindowTextW(hwnd, buffer, length + 1)
                        title = buffer.value
                        
                        if 'Call of Duty' in title or 'WWII' in title:
                            windows.append((hwnd, title))
                return True
            
            windows = []
            ctypes.windll.user32.EnumWindows(
                ctypes.WINFUNCTYPE(ctypes.c_bool, ctypes.POINTER(ctypes.c_int), ctypes.POINTER(ctypes.c_int))(enum_windows_callback), 
                ctypes.byref(ctypes.c_int(0))
            )
            
            if windows:
                self.cod_window = windows[0][0]
                print(f"[FOCUS] Found COD window: {windows[0][1]}")
                return True
            else:
                print("[FOCUS] COD window not found")
                return False
                
        except Exception as e:
            print(f"[FOCUS] Error finding window: {e}")
            return False
    
    def keep_cod_focused(self):
        """Ensure COD window stays focused"""
        if self.cod_window:
            try:
                ctypes.windll.user32.SetForegroundWindow(self.cod_window)
                ctypes.windll.user32.SetActiveWindow(self.cod_window)
                return True
            except Exception as e:
                print(f"[FOCUS] Error setting focus: {e}")
                return False
        return False
    
    def prevent_minimize(self):
        """Prevent COD window from minimizing"""
        if self.cod_window:
            try:
                # Show window if minimized
                ctypes.windll.user32.ShowWindow(self.cod_window, 9)  # SW_RESTORE
                return True
            except Exception as e:
                print(f"[FOCUS] Error preventing minimize: {e}")
                return False
        return False

# Example usage
if __name__ == "__main__":
    # Test the fixed gamepad handler
    gamepad = FixedGamepadHandler()
    focus_manager = WindowFocusManager()
    
    def on_a_button():
        print("[TEST] A button pressed - no focus stealing!")
        focus_manager.keep_cod_focused()
    
    def on_rt_trigger(value):
        print(f"[TEST] RT trigger: {value:.2f} - window stays focused!")
        focus_manager.prevent_minimize()
    
    def on_right_stick(x, y):
        print(f"[TEST] Right stick: ({x:.2f}, {y:.2f})")
    
    # Register callbacks
    gamepad.register_callback('a_button', on_a_button)
    gamepad.register_callback('rt_trigger', on_rt_trigger)
    gamepad.register_callback('right_stick', on_right_stick)
    
    # Start monitoring
    if gamepad.start_monitoring():
        print("[TEST] Gamepad monitoring active - test your controls!")
        print("[TEST] Press Ctrl+C to stop")
        
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            gamepad.stop_monitoring()
            print("[TEST] Stopped")
    else:
        print("[TEST] Failed to start gamepad monitoring")
