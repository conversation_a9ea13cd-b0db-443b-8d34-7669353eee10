#!/usr/bin/env python3
"""
COD WWII Gamepad Enhancer - Main Program
========================================

Simple, clean, WORKING system.
5 files total. Real functionality.

Author: Enhanced System
Version: 1.0
"""

from gui import SimpleGUI

def main():
    """Main entry point"""
    print("="*50)
    print("COD WWII GAMEPAD ENHANCER")
    print("="*50)
    print("")
    print("Features:")
    print("- God Mode (Infinite Health)")
    print("- Infinite Ammo")
    print("- Super Jump (A Button)")
    print("- Rapid Fire (RT Trigger)")
    print("- Aimbot (Auto-targeting)")
    print("- ESP (Enemy Overlay)")
    print("")
    print("Connect your Xbox controller and start COD WWII!")
    print("")
    
    try:
        # Start the GUI
        app = SimpleGUI()
        app.run()
        
    except KeyboardInterrupt:
        print("\n[MAIN] Shutting down...")
    except Exception as e:
        print(f"[MAIN] Error: {e}")

if __name__ == "__main__":
    main()




