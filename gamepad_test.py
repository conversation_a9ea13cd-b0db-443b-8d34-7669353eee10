#!/usr/bin/env python3
"""
Gamepad Test
============

Test gamepad connectivity and button presses.
Make sure your Xbox controller works before using the main hack program.

Author: Enhanced System
Version: 1.0
"""

import pygame
import time

def test_gamepad():
    """Test gamepad functionality"""
    print("=" * 50)
    print("GAMEPAD TEST")
    print("=" * 50)
    
    # Initialize pygame
    pygame.init()
    pygame.joystick.init()
    
    # Check for gamepads
    gamepad_count = pygame.joystick.get_count()
    print(f"Gamepads detected: {gamepad_count}")
    
    if gamepad_count == 0:
        print("❌ No gamepad detected!")
        print("\n💡 To fix this:")
        print("1. Connect your Xbox controller via USB or Bluetooth")
        print("2. Make sure Windows recognizes it")
        print("3. Test it in Windows Game Controllers settings")
        return False
    
    # Initialize first gamepad
    gamepad = pygame.joystick.Joystick(0)
    gamepad.init()
    
    print(f"✅ Gamepad connected:")
    print(f"   Name: {gamepad.get_name()}")
    print(f"   Buttons: {gamepad.get_numbuttons()}")
    print(f"   Axes: {gamepad.get_numaxes()}")
    print(f"   Hats: {gamepad.get_numhats()}")
    
    print("\n" + "=" * 50)
    print("BUTTON TEST")
    print("=" * 50)
    print("Press buttons on your controller (Ctrl+C to stop):")
    print()
    print("Expected mapping:")
    print("  A Button (0) - Toggle God Mode")
    print("  B Button (1) - Toggle Infinite Ammo") 
    print("  X Button (2) - Toggle Infinite Armor")
    print("  Y Button (3) - Toggle Super Speed")
    print()
    
    last_button_states = [False] * gamepad.get_numbuttons()
    
    try:
        while True:
            pygame.event.pump()
            
            # Check all buttons
            for i in range(gamepad.get_numbuttons()):
                button_pressed = gamepad.get_button(i)
                
                # Only print when button state changes
                if button_pressed != last_button_states[i]:
                    if button_pressed:
                        button_name = get_button_name(i)
                        print(f"🎮 Button {i} ({button_name}) PRESSED")
                    
                    last_button_states[i] = button_pressed
            
            # Check axes (analog sticks and triggers)
            left_stick_x = gamepad.get_axis(0)
            left_stick_y = gamepad.get_axis(1)
            right_stick_x = gamepad.get_axis(2) if gamepad.get_numaxes() > 2 else 0
            right_stick_y = gamepad.get_axis(3) if gamepad.get_numaxes() > 3 else 0
            left_trigger = gamepad.get_axis(4) if gamepad.get_numaxes() > 4 else 0
            right_trigger = gamepad.get_axis(5) if gamepad.get_numaxes() > 5 else 0
            
            # Print significant axis movements
            if abs(left_stick_x) > 0.5 or abs(left_stick_y) > 0.5:
                print(f"🕹️  Left Stick: ({left_stick_x:.2f}, {left_stick_y:.2f})")
            
            if abs(right_stick_x) > 0.5 or abs(right_stick_y) > 0.5:
                print(f"🕹️  Right Stick: ({right_stick_x:.2f}, {right_stick_y:.2f})")
            
            if left_trigger > 0.1:
                print(f"🎯 Left Trigger: {left_trigger:.2f}")
            
            if right_trigger > 0.1:
                print(f"🎯 Right Trigger: {right_trigger:.2f}")
            
            time.sleep(0.1)  # 10 FPS update rate
            
    except KeyboardInterrupt:
        print("\n✅ Gamepad test completed!")
        return True

def get_button_name(button_id):
    """Get friendly name for button ID"""
    button_names = {
        0: "A",
        1: "B", 
        2: "X",
        3: "Y",
        4: "LB (Left Bumper)",
        5: "RB (Right Bumper)",
        6: "Back/Select",
        7: "Start",
        8: "Left Stick Click",
        9: "Right Stick Click",
        10: "Guide/Xbox Button"
    }
    
    return button_names.get(button_id, f"Button {button_id}")

if __name__ == "__main__":
    test_gamepad()
