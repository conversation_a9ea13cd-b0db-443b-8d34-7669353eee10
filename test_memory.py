#!/usr/bin/env python3
"""
Test Memory Access
==================

Simple test to see if COD WWII memory access actually works.

Author: Enhanced System
Version: 1.0
"""

from cod_memory import CODWWIIInjector
import time

def test_memory_access():
    """Test if memory access is actually working"""
    print("[TEST] Testing COD WWII memory access...")
    
    try:
        # Create injector
        injector = CODWWIIInjector()
        
        # Test health reading
        print("[TEST] Testing health reading...")
        health_addr = 0x140000000 + 0x0286A5E8
        health = injector.read_int(health_addr)
        print(f"[TEST] Health value: {health}")
        
        if health == 0:
            print("[TEST] Health is 0 - might be wrong offset or game not in match")
        elif 1 <= health <= 1000:
            print("[TEST] Health value looks valid!")
            
            # Test writing health
            print("[TEST] Testing health writing...")
            old_health = health
            injector.write_int(health_addr, 999)
            time.sleep(0.1)
            new_health = injector.read_int(health_addr)
            
            print(f"[TEST] Health change: {old_health} → {new_health}")
            
            if new_health == 999:
                print("[TEST] ✅ MEMORY WRITING WORKS!")
            else:
                print("[TEST] ❌ Memory writing failed")
        else:
            print(f"[TEST] Unexpected health value: {health}")
        
        # Test position reading
        print("[TEST] Testing position reading...")
        pos_base = 0x140000000 + 0x0A0C7388
        x = injector.read_float(pos_base + 0x88)
        y = injector.read_float(pos_base + 0x8C)
        z = injector.read_float(pos_base + 0x84)
        
        print(f"[TEST] Position: ({x:.2f}, {y:.2f}, {z:.2f})")
        
        if x == 0 and y == 0 and z == 0:
            print("[TEST] Position is all zeros - might be wrong offset")
        else:
            print("[TEST] Position looks valid!")
        
        # Test entity reading
        print("[TEST] Testing entity reading...")
        enemies = injector.get_all_enemies()
        print(f"[TEST] Found {len(enemies)} entities")
        
        for i, enemy in enumerate(enemies[:3]):  # Show first 3
            print(f"[TEST] Enemy {i+1}: Health={enemy['health']}, Pos={enemy['position']}")
        
    except Exception as e:
        print(f"[TEST] Error: {e}")

if __name__ == "__main__":
    test_memory_access()



