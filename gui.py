#!/usr/bin/env python3
"""
Simple Working GUI
==================

Clean interface with working toggles for COD WWII.
Every button actually does something real.

Author: Enhanced System
Version: 1.0
"""

import tkinter as tk
from tkinter import ttk
from gamepad import GamepadController
from game_features import CODFeatures

class SimpleGUI:
    """Simple working interface"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("COD WWII Gamepad Enhancer")
        self.root.geometry("500x700")
        self.root.configure(bg='#1a1a1a')
        
        # Initialize systems
        self.gamepad = GamepadController()
        self.features = CODFeatures(self.gamepad)
        
        self.create_interface()
        
        # Status updates
        self.update_status()
    
    def create_interface(self):
        """Create the interface"""
        
        # Title
        title = tk.Label(self.root, text="COD WWII GAMEPAD ENHANCER", 
                        font=('Arial', 16, 'bold'), fg='lime', bg='#1a1a1a')
        title.pack(pady=10)
        
        # Status
        self.status_label = tk.Label(self.root, text="Ready", 
                                    fg='white', bg='#1a1a1a', font=('Arial', 12))
        self.status_label.pack(pady=5)
        
        # Controller status
        controller_status = "CONNECTED" if self.gamepad.is_connected() else "NOT CONNECTED"
        self.controller_label = tk.Label(self.root, text=f"Controller: {controller_status}", 
                                        fg='lime' if self.gamepad.is_connected() else 'red', 
                                        bg='#1a1a1a', font=('Arial', 10))
        self.controller_label.pack(pady=5)
        
        # Features
        self.create_feature_buttons()
        
        # RAGE MODE
        rage_frame = tk.LabelFrame(self.root, text="RAGE MODE", 
                                  fg='red', bg='#1a1a1a', font=('Arial', 14, 'bold'))
        rage_frame.pack(fill='x', padx=20, pady=15)
        
        tk.Button(rage_frame, text="ACTIVATE RAGE MODE", bg='darkred', fg='white',
                 font=('Arial', 14, 'bold'), width=25, height=2,
                 command=self.rage_mode).pack(pady=10)
        
        # Instructions
        inst_frame = tk.LabelFrame(self.root, text="Controls", 
                                  fg='cyan', bg='#1a1a1a', font=('Arial', 12))
        inst_frame.pack(fill='x', padx=20, pady=10)
        
        instructions = [
            "RT = Rapid Fire",
            "A Button = Super Jump", 
            "All features work when toggled ON"
        ]
        
        for inst in instructions:
            tk.Label(inst_frame, text=inst, fg='white', bg='#1a1a1a').pack(anchor='w', padx=10)
    
    def create_feature_buttons(self):
        """Create feature toggle buttons"""
        
        # Godmode
        godmode_frame = tk.LabelFrame(self.root, text="GOD MODE", 
                                     fg='purple', bg='#1a1a1a', font=('Arial', 12, 'bold'))
        godmode_frame.pack(fill='x', padx=20, pady=5)
        
        tk.Button(godmode_frame, text="TOGGLE GODMODE", bg='purple', fg='white',
                 font=('Arial', 12, 'bold'), width=20, height=1,
                 command=self.toggle_godmode).pack(pady=5)
        
        # Infinite Ammo
        ammo_frame = tk.LabelFrame(self.root, text="INFINITE AMMO", 
                                  fg='orange', bg='#1a1a1a', font=('Arial', 12, 'bold'))
        ammo_frame.pack(fill='x', padx=20, pady=5)
        
        tk.Button(ammo_frame, text="TOGGLE INFINITE AMMO", bg='orange', fg='black',
                 font=('Arial', 12, 'bold'), width=20, height=1,
                 command=self.toggle_infinite_ammo).pack(pady=5)
        
        # Super Jump
        jump_frame = tk.LabelFrame(self.root, text="SUPER JUMP", 
                                  fg='blue', bg='#1a1a1a', font=('Arial', 12, 'bold'))
        jump_frame.pack(fill='x', padx=20, pady=5)
        
        tk.Button(jump_frame, text="TOGGLE SUPER JUMP", bg='blue', fg='white',
                 font=('Arial', 12, 'bold'), width=20, height=1,
                 command=self.toggle_super_jump).pack(pady=5)
        
        # Rapid Fire
        fire_frame = tk.LabelFrame(self.root, text="RAPID FIRE", 
                                  fg='yellow', bg='#1a1a1a', font=('Arial', 12, 'bold'))
        fire_frame.pack(fill='x', padx=20, pady=5)
        
        tk.Button(fire_frame, text="TOGGLE RAPID FIRE", bg='yellow', fg='black',
                 font=('Arial', 12, 'bold'), width=20, height=1,
                 command=self.toggle_rapid_fire).pack(pady=5)
        
        # Aimbot
        aim_frame = tk.LabelFrame(self.root, text="AIMBOT", 
                                 fg='red', bg='#1a1a1a', font=('Arial', 12, 'bold'))
        aim_frame.pack(fill='x', padx=20, pady=5)
        
        tk.Button(aim_frame, text="TOGGLE AIMBOT", bg='red', fg='white',
                 font=('Arial', 12, 'bold'), width=20, height=1,
                 command=self.toggle_aimbot).pack(pady=5)
        
        # ESP REMOVED - was blocking screen
        esp_frame = tk.LabelFrame(self.root, text="ESP (DISABLED)", 
                                 fg='gray', bg='#1a1a1a', font=('Arial', 12))
        esp_frame.pack(fill='x', padx=20, pady=5)
        
        tk.Label(esp_frame, text="ESP removed - was blocking screen clicks", 
                fg='gray', bg='#1a1a1a').pack(pady=5)
    
    def toggle_godmode(self):
        self.features.toggle_godmode()
        self.update_status()
    
    def toggle_infinite_ammo(self):
        self.features.toggle_infinite_ammo()
        self.update_status()
    
    def toggle_super_jump(self):
        self.features.toggle_super_jump()
        self.update_status()
    
    def toggle_rapid_fire(self):
        self.features.toggle_rapid_fire()
        self.update_status()
    
    def toggle_aimbot(self):
        self.features.toggle_aimbot()
        self.update_status()
    
    def toggle_esp(self):
        print("[ESP] ESP disabled - was causing screen blocking")
        return
    
    def rage_mode(self):
        """Activate all features"""
        self.features.activate_rage_mode()
        self.update_status()
    
    def update_status(self):
        """Update status display"""
        active_features = []
        
        if self.features.godmode_active:
            active_features.append("GODMODE")
        if self.features.infinite_ammo_active:
            active_features.append("INFINITE AMMO")
        if self.features.super_jump_active:
            active_features.append("SUPER JUMP")
        if self.features.rapid_fire_active:
            active_features.append("RAPID FIRE")
        if self.features.aimbot_active:
            active_features.append("AIMBOT")
        # ESP removed
        
        if active_features:
            status_text = "ACTIVE: " + ", ".join(active_features)
            self.status_label.config(text=status_text, fg='lime')
        else:
            self.status_label.config(text="No features active", fg='white')
        
        # Schedule next update
        self.root.after(1000, self.update_status)
    
    def run(self):
        """Start the interface"""
        print("[GUI] Simple COD WWII enhancer ready!")
        print("[GUI] Toggle features ON/OFF with buttons")
        self.root.mainloop()
