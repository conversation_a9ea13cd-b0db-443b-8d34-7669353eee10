#!/usr/bin/env python3
"""
Quick Process Check
==================

Quick check to see what Wolfenstein 2 processes are running.

Author: Enhanced System
Version: 1.0
"""

import psutil
import time

def check_wolfenstein_processes():
    """Check for any Wolfenstein 2 related processes"""
    print("=" * 60)
    print("QUICK WOLFENSTEIN 2 PROCESS CHECK")
    print("=" * 60)
    
    # All possible process names
    wolfenstein_keywords = [
        "wolfenstein", "colossus", "newcolossus", "bj2", "wolf", "bethesda"
    ]
    
    found_processes = []
    
    print("🔍 Scanning all running processes...")
    
    for proc in psutil.process_iter(['pid', 'name', 'exe', 'status']):
        try:
            proc_name = proc.info['name'].lower()
            
            # Check if any keyword matches
            for keyword in wolfenstein_keywords:
                if keyword in proc_name:
                    found_processes.append({
                        'name': proc.info['name'],
                        'pid': proc.info['pid'],
                        'exe': proc.info.get('exe', 'Unknown'),
                        'status': proc.info.get('status', 'unknown')
                    })
                    break
                    
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            continue
    
    if found_processes:
        print(f"\n✅ Found {len(found_processes)} Wolfenstein-related process(es):")
        print("-" * 60)
        
        for i, proc in enumerate(found_processes, 1):
            print(f"{i}. Process: {proc['name']}")
            print(f"   PID: {proc['pid']}")
            print(f"   Status: {proc['status']}")
            print(f"   Path: {proc['exe']}")
            print()
            
        return found_processes
    else:
        print("\n❌ No Wolfenstein 2 processes found!")
        print("\nMake sure:")
        print("1. Wolfenstein 2: The New Colossus is running")
        print("2. The game is launched (even if paused/minimized)")
        print("3. Steam is running if it's the Steam version")
        
        return []

def check_all_game_processes():
    """Check for any game-related processes"""
    print("\n" + "=" * 60)
    print("ALL GAME PROCESSES CHECK")
    print("=" * 60)
    
    game_keywords = [
        "steam", "epic", "origin", "uplay", "bethesda", "launcher",
        "game", ".exe"
    ]
    
    game_processes = []
    
    for proc in psutil.process_iter(['pid', 'name', 'exe']):
        try:
            proc_name = proc.info['name'].lower()
            
            # Look for game-related processes
            if any(keyword in proc_name for keyword in game_keywords):
                if proc_name.endswith('.exe') and len(proc_name) > 10:  # Likely game executable
                    game_processes.append({
                        'name': proc.info['name'],
                        'pid': proc.info['pid'],
                        'exe': proc.info.get('exe', 'Unknown')
                    })
                    
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            continue
    
    if game_processes:
        print(f"\n🎮 Found {len(game_processes)} potential game process(es):")
        print("-" * 60)
        
        for i, proc in enumerate(game_processes[:10], 1):  # Show first 10
            print(f"{i}. {proc['name']} (PID: {proc['pid']})")
            
        if len(game_processes) > 10:
            print(f"... and {len(game_processes) - 10} more")
    else:
        print("\n❌ No game processes detected")

if __name__ == "__main__":
    # Check for Wolfenstein specifically
    wolf_processes = check_wolfenstein_processes()
    
    # If no Wolfenstein found, check for other games
    if not wolf_processes:
        check_all_game_processes()
    
    print("\n" + "=" * 60)
    print("PROCESS CHECK COMPLETE")
    print("=" * 60)
    
    if wolf_processes:
        print("✅ Wolfenstein 2 detected - you can use the hack system!")
        print("Run: python wolf2_paused_compatible.py")
    else:
        print("❌ Wolfenstein 2 not detected")
        print("Start the game first, then run the hack system")
