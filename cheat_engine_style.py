#!/usr/bin/env python3
"""
Cheat Engine Style Scanner
==========================

Works exactly like Cheat Engine:
1. Scan for specific value (like your current health: 100)
2. Take damage (health becomes 75)
3. <PERSON>an again for 75
4. Repeat until you find the exact address
5. Modify that address

Author: Enhanced System
Version: CE STYLE 1.0
"""

import pymem
import pymem.process
import time
import threading

class CheatEngineStyleScanner:
    """Cheat Engine style memory scanner"""
    
    def __init__(self):
        self.pm = None
        self.base_address = None
        self.scan_results = {}  # address -> value
        self.health_address = None
        self.ammo_address = None
        self.godmode_active = False
        self.infinite_ammo_active = False
    
    def connect(self):
        """Connect to COD WWII"""
        try:
            self.pm = pymem.Pymem("s2_mp64_ship.exe")
            self.base_address = self.pm.base_address
            print(f"✅ Connected to COD WWII - Base: 0x{self.base_address:X}")
            return True
        except Exception as e:
            print(f"❌ Connection failed: {e}")
            return False
    
    def first_scan(self, value):
        """First scan - find all addresses with specific value"""
        print(f"🔍 First scan for value: {value}")
        print("This may take a moment...")
        
        self.scan_results = {}
        
        # Scan game memory regions
        scan_ranges = [
            (self.base_address + 0x1000000, 0x2000000),   # 16-48MB
            (self.base_address + 0x5000000, 0x2000000),   # 80-112MB
            (self.base_address + 0xA000000, 0x2000000),   # 160-192MB
        ]
        
        total_found = 0
        
        for start_addr, size in scan_ranges:
            print(f"Scanning 0x{start_addr:X} - 0x{start_addr + size:X}")
            
            for addr in range(start_addr, start_addr + size, 4):
                try:
                    read_value = self.pm.read_int(addr)
                    
                    if read_value == value:
                        self.scan_results[addr] = value
                        total_found += 1
                        
                        if total_found % 100 == 0:
                            print(f"Found {total_found} matches...")
                        
                        # Limit results to prevent memory issues
                        if total_found >= 10000:
                            print("Limiting to first 10,000 results")
                            break
                            
                except:
                    continue
            
            if total_found >= 10000:
                break
        
        print(f"✅ First scan complete: {len(self.scan_results)} addresses found")
        return len(self.scan_results) > 0
    
    def next_scan(self, new_value):
        """Next scan - filter previous results for new value"""
        print(f"🔍 Next scan for value: {new_value}")
        
        if not self.scan_results:
            print("❌ No previous scan results!")
            return False
        
        filtered_results = {}
        
        for addr in self.scan_results:
            try:
                current_value = self.pm.read_int(addr)
                
                if current_value == new_value:
                    filtered_results[addr] = new_value
                    
            except:
                continue
        
        self.scan_results = filtered_results
        print(f"✅ Next scan complete: {len(self.scan_results)} addresses remaining")
        
        # Show remaining addresses if few enough
        if len(self.scan_results) <= 20:
            print("Remaining addresses:")
            for addr, value in self.scan_results.items():
                offset = addr - self.base_address
                print(f"  0x{addr:X} (base+0x{offset:X}) = {value}")
        
        return len(self.scan_results) > 0
    
    def set_health_address(self, address):
        """Set the health address manually"""
        if address in self.scan_results:
            self.health_address = address
            print(f"✅ Health address set: 0x{address:X}")
            return True
        else:
            print("❌ Address not in scan results!")
            return False
    
    def set_ammo_address(self, address):
        """Set the ammo address manually"""
        if address in self.scan_results:
            self.ammo_address = address
            print(f"✅ Ammo address set: 0x{address:X}")
            return True
        else:
            print("❌ Address not in scan results!")
            return False
    
    def test_address(self, address, test_value):
        """Test writing to an address"""
        try:
            original = self.pm.read_int(address)
            print(f"Original value at 0x{address:X}: {original}")
            
            # Write test value
            self.pm.write_int(address, test_value)
            time.sleep(0.1)
            
            # Read back
            new_value = self.pm.read_int(address)
            print(f"After writing {test_value}: {new_value}")
            
            # Restore original
            self.pm.write_int(address, original)
            print(f"Restored to: {original}")
            
            return new_value == test_value
            
        except Exception as e:
            print(f"❌ Test failed: {e}")
            return False
    
    def start_godmode(self):
        """Start godmode using found health address"""
        if not self.health_address:
            print("❌ No health address set!")
            return
        
        if self.godmode_active:
            print("Godmode already active")
            return
        
        self.godmode_active = True
        print(f"🛡️  Starting godmode at 0x{self.health_address:X}")
        
        def godmode_worker():
            while self.godmode_active:
                try:
                    current_health = self.pm.read_int(self.health_address)
                    
                    # If health is low, restore it
                    if 1 <= current_health <= 150:
                        self.pm.write_int(self.health_address, 200)
                        print(f"🛡️  Health: {current_health} → 200")
                    
                    time.sleep(0.1)
                    
                except Exception as e:
                    print(f"Godmode error: {e}")
                    time.sleep(1.0)
        
        threading.Thread(target=godmode_worker, daemon=True).start()
    
    def start_infinite_ammo(self):
        """Start infinite ammo using found ammo address"""
        if not self.ammo_address:
            print("❌ No ammo address set!")
            return
        
        if self.infinite_ammo_active:
            print("Infinite ammo already active")
            return
        
        self.infinite_ammo_active = True
        print(f"🔫 Starting infinite ammo at 0x{self.ammo_address:X}")
        
        def ammo_worker():
            while self.infinite_ammo_active:
                try:
                    current_ammo = self.pm.read_int(self.ammo_address)
                    
                    # If ammo is low, restore it
                    if 0 <= current_ammo <= 50:
                        self.pm.write_int(self.ammo_address, 99)
                        print(f"🔫 Ammo: {current_ammo} → 99")
                    
                    time.sleep(0.2)
                    
                except Exception as e:
                    print(f"Infinite ammo error: {e}")
                    time.sleep(1.0)
        
        threading.Thread(target=ammo_worker, daemon=True).start()
    
    def stop_godmode(self):
        """Stop godmode"""
        self.godmode_active = False
        print("🛡️  Godmode stopped")
    
    def stop_infinite_ammo(self):
        """Stop infinite ammo"""
        self.infinite_ammo_active = False
        print("🔫 Infinite ammo stopped")
    
    def stop_all(self):
        """Stop all features"""
        self.godmode_active = False
        self.infinite_ammo_active = False
        print("⏹️  All features stopped")

def main():
    """Main Cheat Engine style interface"""
    print("=" * 60)
    print("CHEAT ENGINE STYLE SCANNER")
    print("=" * 60)
    print("Works exactly like Cheat Engine!")
    print()
    
    scanner = CheatEngineStyleScanner()
    
    if not scanner.connect():
        return
    
    print("\n" + "=" * 60)
    print("CHEAT ENGINE STYLE SCANNING")
    print("=" * 60)
    print("Example workflow:")
    print("1. Look at your health in game (e.g., 100)")
    print("2. Do first scan for 100")
    print("3. Take damage (health becomes 75)")
    print("4. Do next scan for 75")
    print("5. Repeat until 1-5 addresses remain")
    print("6. Test addresses to find the right one")
    print()
    
    while True:
        print("\nOptions:")
        print("1. First scan (scan for specific value)")
        print("2. Next scan (filter previous results)")
        print("3. Show current results")
        print("4. Test address")
        print("5. Set health address")
        print("6. Set ammo address")
        print("7. Start godmode")
        print("8. Start infinite ammo")
        print("9. Stop all")
        print("0. Exit")
        
        choice = input("\nChoice: ").strip()
        
        if choice == '1':
            try:
                value = int(input("Enter value to scan for: "))
                scanner.first_scan(value)
            except ValueError:
                print("Invalid value!")
        
        elif choice == '2':
            try:
                value = int(input("Enter new value to scan for: "))
                scanner.next_scan(value)
            except ValueError:
                print("Invalid value!")
        
        elif choice == '3':
            if scanner.scan_results:
                print(f"Current results: {len(scanner.scan_results)} addresses")
                if len(scanner.scan_results) <= 50:
                    for addr, value in list(scanner.scan_results.items())[:50]:
                        offset = addr - scanner.base_address
                        print(f"  0x{addr:X} (base+0x{offset:X}) = {value}")
                else:
                    print("Too many results to display. Do more scans to narrow down.")
            else:
                print("No scan results")
        
        elif choice == '4':
            try:
                addr_str = input("Enter address to test (hex, e.g., 7FF7CA1B0000): ")
                address = int(addr_str, 16)
                test_value = int(input("Enter test value: "))
                scanner.test_address(address, test_value)
            except ValueError:
                print("Invalid input!")
        
        elif choice == '5':
            try:
                addr_str = input("Enter health address (hex): ")
                address = int(addr_str, 16)
                scanner.set_health_address(address)
            except ValueError:
                print("Invalid address!")
        
        elif choice == '6':
            try:
                addr_str = input("Enter ammo address (hex): ")
                address = int(addr_str, 16)
                scanner.set_ammo_address(address)
            except ValueError:
                print("Invalid address!")
        
        elif choice == '7':
            scanner.start_godmode()
        
        elif choice == '8':
            scanner.start_infinite_ammo()
        
        elif choice == '9':
            scanner.stop_all()
        
        elif choice == '0':
            scanner.stop_all()
            print("Goodbye!")
            break
        
        else:
            print("Invalid choice!")

if __name__ == "__main__":
    main()
