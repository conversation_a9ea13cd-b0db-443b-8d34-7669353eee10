#!/usr/bin/env python3
"""
COD WWII Offset Finder
======================

Scans memory to find REAL working offsets for Nazi Zombies.
Use this when the provided offsets don't work.

Author: Enhanced System
Version: 1.0
"""

import pymem
import pymem.process
import struct
import time

class OffsetFinder:
    """Find working memory offsets for COD WWII"""
    
    def __init__(self):
        self.pm = None
        self.base_address = None
        
    def connect(self):
        """Connect to COD WWII"""
        try:
            self.pm = pymem.Pymem("s2_mp64_ship.exe")
            self.base_address = self.pm.base_address
            print(f"[FINDER] Connected - Base: 0x{self.base_address:X}")
            return True
        except Exception as e:
            print(f"[FINDER] Connection failed: {e}")
            return False
    
    def scan_for_health_values(self):
        """Scan memory for potential health values"""
        print("[FINDER] Scanning for health values (1-200)...")
        print("[FINDER] Make sure you're in Nazi Zombies with visible health!")
        
        candidates = []
        scan_start = self.base_address
        scan_end = self.base_address + 0x20000000  # 512MB scan
        
        try:
            for addr in range(scan_start, scan_end, 4):
                try:
                    value = self.pm.read_int(addr)
                    
                    # Look for health-like values
                    if 50 <= value <= 200:
                        candidates.append((addr, value))
                        
                        if len(candidates) % 1000 == 0:
                            print(f"[FINDER] Found {len(candidates)} candidates...")
                        
                        # Stop after finding many candidates
                        if len(candidates) > 10000:
                            break
                            
                except:
                    continue
            
            print(f"[FINDER] Found {len(candidates)} potential health addresses")
            
            # Show top candidates
            for i, (addr, value) in enumerate(candidates[:20]):
                offset = addr - self.base_address
                print(f"  {i+1}. 0x{addr:X} (offset: 0x{offset:X}) = {value}")
            
            return candidates
            
        except Exception as e:
            print(f"[FINDER] Scan error: {e}")
            return []
    
    def scan_for_pointers(self):
        """Scan for pointer chains to health"""
        print("[FINDER] Scanning for health pointer chains...")
        
        # Common pointer base areas
        pointer_areas = [
            (0xA000000, 0xB000000),   # Around the provided offsets
            (0x1000000, 0x2000000),   # Lower memory
            (0x10000000, 0x20000000), # Higher memory
        ]
        
        for start_offset, end_offset in pointer_areas:
            print(f"[FINDER] Scanning area 0x{start_offset:X} - 0x{end_offset:X}")
            
            for offset in range(start_offset, end_offset, 4):
                try:
                    addr = self.base_address + offset
                    ptr_value = self.pm.read_int(addr)
                    
                    # Check if this looks like a valid pointer
                    if 0x10000 < ptr_value < 0x7FFFFFFFFFFF:
                        try:
                            # Try to read what it points to
                            pointed_value = self.pm.read_int(ptr_value)
                            
                            # Check if pointed value could be health
                            if 1 <= pointed_value <= 1000:
                                print(f"[FINDER] Pointer chain: 0x{addr:X} -> 0x{ptr_value:X} = {pointed_value}")
                                
                                # Try common health offsets from this pointer
                                for health_offset in [0x0, 0x4, 0x8, 0x2DC, 0xF8]:
                                    try:
                                        health_val = self.pm.read_int(ptr_value + health_offset)
                                        if 1 <= health_val <= 1000:
                                            print(f"    -> +0x{health_offset:X} = {health_val} (potential health!)")
                                    except:
                                        continue
                        except:
                            continue
                            
                except:
                    continue
    
    def test_provided_offsets(self):
        """Test the provided July 2025 offsets"""
        print("[FINDER] Testing provided July 2025 offsets...")
        
        offsets_to_test = {
            'health_base': 0xA2D7DC8,
            'username_base': 0x0E4650B0,
            'position_base': 0xA0C7388,
            'entity_list': 0xA0C7130,
            'current_ammo_base': 0x10948C8,
            'zombie_array': 0xA0D35B0,
            'refdef': 0x8CE9968,
        }
        
        for name, offset in offsets_to_test.items():
            try:
                addr = self.base_address + offset
                value = self.pm.read_int(addr)
                
                print(f"[FINDER] {name}: 0x{addr:X} = 0x{value:X} ({value})")
                
                # For pointer-like values, try to dereference
                if 0x10000 < value < 0x7FFFFFFFFFFF:
                    try:
                        deref_value = self.pm.read_int(value)
                        print(f"    -> Dereferenced: 0x{value:X} = {deref_value}")
                        
                        # Try health offset
                        if name == 'health_base':
                            health_addr = value + 0x2DC
                            health = self.pm.read_int(health_addr)
                            print(f"    -> Health (+0x2DC): {health}")
                            
                    except:
                        print(f"    -> Cannot dereference 0x{value:X}")
                
            except Exception as e:
                print(f"[FINDER] {name}: Error reading - {e}")
    
    def interactive_memory_explorer(self):
        """Interactive memory exploration"""
        print("[FINDER] Interactive Memory Explorer")
        print("Commands:")
        print("  read <hex_address> - Read 4 bytes from address")
        print("  scan <min> <max> - Scan for values in range")
        print("  ptr <hex_address> - Follow pointer chain")
        print("  quit - Exit")
        
        while True:
            try:
                cmd = input("\n[EXPLORER] > ").strip().lower()
                
                if cmd == 'quit':
                    break
                elif cmd.startswith('read '):
                    addr_str = cmd.split()[1]
                    addr = int(addr_str, 16)
                    try:
                        value = self.pm.read_int(addr)
                        print(f"0x{addr:X} = {value} (0x{value:X})")
                    except Exception as e:
                        print(f"Error reading 0x{addr:X}: {e}")
                        
                elif cmd.startswith('scan '):
                    parts = cmd.split()
                    min_val = int(parts[1])
                    max_val = int(parts[2])
                    
                    print(f"Scanning for values {min_val}-{max_val}...")
                    count = 0
                    
                    for offset in range(0, 0x10000000, 4):
                        try:
                            addr = self.base_address + offset
                            value = self.pm.read_int(addr)
                            
                            if min_val <= value <= max_val:
                                print(f"0x{addr:X} (offset: 0x{offset:X}) = {value}")
                                count += 1
                                
                                if count >= 20:
                                    print("... (showing first 20 results)")
                                    break
                        except:
                            continue
                            
                elif cmd.startswith('ptr '):
                    addr_str = cmd.split()[1]
                    addr = int(addr_str, 16)
                    try:
                        ptr = self.pm.read_int(addr)
                        print(f"0x{addr:X} -> 0x{ptr:X}")
                        
                        if 0x10000 < ptr < 0x7FFFFFFFFFFF:
                            try:
                                value = self.pm.read_int(ptr)
                                print(f"0x{ptr:X} = {value}")
                            except:
                                print(f"Cannot read from 0x{ptr:X}")
                        else:
                            print("Not a valid pointer")
                    except Exception as e:
                        print(f"Error: {e}")
                        
                else:
                    print("Unknown command")
                    
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"Error: {e}")

if __name__ == "__main__":
    finder = OffsetFinder()
    
    if finder.connect():
        print("\n[FINDER] COD WWII Offset Finder Ready!")
        print("Make sure you're in an ACTIVE Nazi Zombies match with visible health!")
        print()
        
        while True:
            print("Options:")
            print("1. Test provided July 2025 offsets")
            print("2. Scan for health values")
            print("3. Scan for pointer chains")
            print("4. Interactive memory explorer")
            print("5. Exit")
            
            choice = input("\nChoice: ").strip()
            
            if choice == '1':
                finder.test_provided_offsets()
            elif choice == '2':
                finder.scan_for_health_values()
            elif choice == '3':
                finder.scan_for_pointers()
            elif choice == '4':
                finder.interactive_memory_explorer()
            elif choice == '5':
                break
            else:
                print("Invalid choice")
    else:
        print("[FINDER] Failed to connect to COD WWII")
