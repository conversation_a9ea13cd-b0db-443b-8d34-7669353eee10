#!/usr/bin/env python3
"""
Simple Test - Answer Your Questions
===================================

1. How does system know who the enemy is?
2. How does it know my game process? 
3. How do we know memory reading is correct?
4. How find out why mods not working?

For Nazi Zombies mode specifically.

Author: Enhanced System
Version: 1.0
"""

import psutil
import ctypes
import ctypes.wintypes
import struct
import win32gui
import win32process

def answer_your_questions():
    """Answer the fundamental questions"""
    
    print("=" * 60)
    print("ANSWERING YOUR QUESTIONS")
    print("=" * 60)
    
    # Question 1: How does system know who the enemy is?
    print("\n1. HOW DOES SYSTEM KNOW WHO THE ENEMY IS?")
    print("   In Nazi Zombies:")
    print("   - Enemies = Zombies (not other players)")
    print("   - Zombies have different memory structure")
    print("   - Your offsets mention 'ZombieArray: 0xA0D35B0'")
    print("   - We need to read from ZOMBIE array, not player array")
    print("   - Each zombie has health, position, AI state")
    
    # Question 2: How does it know my game process?
    print("\n2. HOW DOES IT KNOW MY GAME PROCESS?")
    print("   Process detection:")
    
    for proc in psutil.process_iter(['pid', 'name', 'exe']):
        try:
            if 's2_mp64_ship.exe' in proc.info['name']:
                print(f"   ✅ Found: {proc.info['name']} (PID: {proc.info['pid']})")
                print(f"   ✅ Path: {proc.info['exe']}")
                print("   ✅ This is YOUR COD WWII game process")
                break
        except Exception:
            continue
    
    # Question 3: How do we know memory reading is correct?
    print("\n3. HOW DO WE KNOW MEMORY READING IS CORRECT?")
    print("   Current status: ALL ZEROS = WRONG OFFSETS")
    print("   - Health reading: 0 (should be 1-100)")
    print("   - Position reading: (0,0,0) (should be real coordinates)")
    print("   - This means:")
    print("     a) Wrong base address")
    print("     b) Wrong offsets for Nazi Zombies mode")
    print("     c) Game has anti-cheat protection")
    print("     d) Not in active game/match")
    
    # Question 4: How can we find out why mods not working?
    print("\n4. HOW TO FIND OUT WHY MODS NOT WORKING?")
    print("   Debugging steps:")
    print("   a) Verify you're IN Nazi Zombies match (not menu)")
    print("   b) Use Cheat Engine to find REAL offsets")
    print("   c) Scan for your current health value in memory")
    print("   d) Find zombie health values manually")
    print("   e) Test with different COD WWII versions")
    
    print("\n" + "=" * 60)
    print("RECOMMENDATIONS FOR NAZI ZOMBIES")
    print("=" * 60)
    
    print("\nFor Nazi Zombies specifically:")
    print("1. Start a Nazi Zombies match")
    print("2. Use Cheat Engine to scan for your health (100)")
    print("3. Take damage, scan for new health value")
    print("4. Find the REAL health address")
    print("5. Look for zombie entities in ZombieArray")
    print("6. Zombie offsets might be different from multiplayer")
    
    print("\nYour provided offsets might be for MULTIPLAYER only!")
    print("Nazi Zombies probably has different memory layout.")

def test_nazi_zombies_detection():
    """Test zombie-specific detection"""
    print("\n" + "=" * 60)
    print("NAZI ZOMBIES SPECIFIC TEST")
    print("=" * 60)
    
    # Find process
    cod_pid = None
    for proc in psutil.process_iter(['pid', 'name']):
        if 's2_mp64_ship.exe' in proc.info['name']:
            cod_pid = proc.info['pid']
            break
    
    if not cod_pid:
        print("[ZOMBIE TEST] COD WWII not found!")
        return
    
    # Get process handle
    try:
        handle = ctypes.windll.kernel32.OpenProcess(0x1F0FFF, False, cod_pid)
        if not handle:
            print("[ZOMBIE TEST] Could not get process handle")
            return
        
        print(f"[ZOMBIE TEST] Got process handle for PID {cod_pid}")
        
        # Check if in Nazi Zombies by looking at window title
        def enum_windows_callback(hwnd, windows):
            if win32gui.IsWindowVisible(hwnd):
                try:
                    _, window_pid = win32process.GetWindowThreadProcessId(hwnd)
                    if window_pid == cod_pid:
                        title = win32gui.GetWindowText(hwnd)
                        if title:
                            windows.append(title)
                except Exception:
                    pass
            return True
        
        windows = []
        win32gui.EnumWindows(enum_windows_callback, windows)
        
        for title in windows:
            print(f"[ZOMBIE TEST] Window: {title}")
            if 'Nazi Zombies' in title or 'Zombies' in title:
                print("[ZOMBIE TEST] ✅ CONFIRMED: In Nazi Zombies mode")
            elif 'Call of Duty' in title:
                print("[ZOMBIE TEST] ⚠️  COD WWII running but mode unknown")
                print("[ZOMBIE TEST] Make sure you're IN a zombies match, not menu")
        
        # Try reading from zombie-specific array
        print("[ZOMBIE TEST] Testing zombie array access...")
        zombie_base = 0x140000000 + 0xA0D35B0  # Your ZombieArray offset
        
        def read_int(addr):
            try:
                buffer = ctypes.create_string_buffer(4)
                bytes_read = ctypes.c_size_t(0)
                result = ctypes.windll.kernel32.ReadProcessMemory(
                    handle, ctypes.c_void_p(addr), buffer, 4, ctypes.byref(bytes_read))
                if result and bytes_read.value == 4:
                    return struct.unpack('<i', buffer.raw)[0]
            except Exception:
                pass
            return 0
        
        # Test zombie array
        zombie_value = read_int(zombie_base)
        print(f"[ZOMBIE TEST] Zombie array value: {zombie_value} (0x{zombie_value:X})")
        
        if zombie_value == 0:
            print("[ZOMBIE TEST] ❌ Zombie array reading zeros")
            print("[ZOMBIE TEST] Possible causes:")
            print("  - Wrong base address (not 0x140000000)")
            print("  - Not in active zombies match")
            print("  - Different offsets for your COD WWII version")
            print("  - Game has memory protection")
        else:
            print("[ZOMBIE TEST] ✅ Zombie array has data!")
        
        ctypes.windll.kernel32.CloseHandle(handle)
        
    except Exception as e:
        print(f"[ZOMBIE TEST] Error: {e}")

if __name__ == "__main__":
    answer_your_questions()
    test_nazi_zombies_detection()

