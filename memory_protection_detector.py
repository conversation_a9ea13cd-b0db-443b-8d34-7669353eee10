#!/usr/bin/env python3
"""
Memory Protection Detector
==========================

Detects what's blocking memory access to games.
Checks for anti-cheat, DEP, ASLR, Windows Defender, and other protections.

Author: Enhanced System
Version: 1.0
"""

import ctypes
import ctypes.wintypes
import psutil
import subprocess
import os
import sys
import winreg

class MemoryProtectionDetector:
    """Detect memory protection mechanisms"""
    
    def __init__(self):
        self.process_id = None
        self.process_handle = None
        
    def check_admin_privileges(self):
        """Check if running as administrator"""
        try:
            is_admin = ctypes.windll.shell32.IsUserAnAdmin()
            if is_admin:
                print("✅ Running as Administrator")
                return True
            else:
                print("❌ NOT running as Administrator")
                print("   → This is likely the main issue!")
                print("   → Right-click terminal/script and 'Run as Administrator'")
                return False
        except:
            print("❓ Cannot determine admin status")
            return False
    
    def check_windows_defender(self):
        """Check Windows Defender real-time protection"""
        print("\n🛡️  Checking Windows Defender:")
        
        try:
            # Method 1: PowerShell command
            result = subprocess.run([
                'powershell', '-Command', 
                'Get-MpPreference | Select-Object DisableRealtimeMonitoring'
            ], capture_output=True, text=True, shell=True, timeout=10)
            
            if 'False' in result.stdout:
                print("   ❌ Windows Defender Real-time Protection is ON")
                print("   → This WILL block memory access to games")
                print("   → Temporarily disable it or add exclusions")
                return False
            elif 'True' in result.stdout:
                print("   ✅ Windows Defender Real-time Protection is OFF")
                return True
            else:
                print("   ❓ Cannot determine Defender status")
                
        except Exception as e:
            print(f"   ❓ Cannot check Windows Defender: {e}")
        
        # Method 2: Registry check
        try:
            key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, 
                               r"SOFTWARE\Microsoft\Windows Defender\Real-Time Protection")
            value, _ = winreg.QueryValueEx(key, "DisableRealtimeMonitoring")
            winreg.CloseKey(key)
            
            if value == 1:
                print("   ✅ Windows Defender disabled (registry)")
                return True
            else:
                print("   ❌ Windows Defender enabled (registry)")
                return False
                
        except:
            print("   ❓ Cannot read Defender registry")
        
        return False
    
    def check_antivirus_processes(self):
        """Check for antivirus processes"""
        print("\n🚫 Checking for Antivirus/Security Software:")
        
        antivirus_processes = [
            # Common antivirus
            'avast', 'avg', 'norton', 'mcafee', 'kaspersky', 'bitdefender',
            'eset', 'trend', 'sophos', 'malwarebytes', 'webroot',
            
            # Security software
            'defender', 'smartscreen', 'wdfilter', 'msmpeng',
            
            # Anti-cheat systems
            'easyanticheat', 'battleye', 'vanguard', 'faceit', 'esea'
        ]
        
        found_security = []
        
        for proc in psutil.process_iter(['name']):
            try:
                proc_name = proc.info['name'].lower()
                for av in antivirus_processes:
                    if av in proc_name:
                        found_security.append(proc.info['name'])
                        break
            except:
                continue
        
        if found_security:
            print(f"   ❌ Security software detected: {found_security}")
            print("   → These may block memory access!")
            print("   → Add your hack tools to exclusions")
            return False
        else:
            print("   ✅ No obvious security software detected")
            return True
    
    def check_dep_protection(self):
        """Check Data Execution Prevention"""
        print("\n🛡️  Checking DEP (Data Execution Prevention):")
        
        try:
            result = subprocess.run(['bcdedit', '/enum'], capture_output=True, text=True, shell=True)
            if 'nx' in result.stdout.lower():
                if 'optout' in result.stdout.lower():
                    print("   ⚠️  DEP enabled for all programs except opted-out")
                    print("   → May interfere with memory access")
                    return False
                elif 'optin' in result.stdout.lower():
                    print("   ✅ DEP enabled only for Windows programs")
                    return True
                else:
                    print("   ❌ DEP fully enabled")
                    print("   → This WILL block memory modification")
                    return False
            else:
                print("   ✅ DEP not detected or disabled")
                return True
                
        except Exception as e:
            print(f"   ❓ Cannot check DEP: {e}")
            return True
    
    def check_aslr_protection(self):
        """Check Address Space Layout Randomization"""
        print("\n🎲 Checking ASLR (Address Space Layout Randomization):")
        
        try:
            # Check system ASLR policy
            result = subprocess.run([
                'powershell', '-Command',
                'Get-ProcessMitigation -System | Select-Object ASLR'
            ], capture_output=True, text=True, shell=True, timeout=10)
            
            if 'Enable' in result.stdout:
                print("   ❌ ASLR is ENABLED system-wide")
                print("   → This randomizes memory addresses")
                print("   → Hardcoded offsets will fail")
                return False
            else:
                print("   ✅ ASLR appears disabled or limited")
                return True
                
        except Exception as e:
            print(f"   ❓ Cannot check ASLR: {e}")
            return True
    
    def check_uac_settings(self):
        """Check User Account Control settings"""
        print("\n🔒 Checking UAC (User Account Control):")
        
        try:
            key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE,
                               r"SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System")
            value, _ = winreg.QueryValueEx(key, "EnableLUA")
            winreg.CloseKey(key)
            
            if value == 1:
                print("   ⚠️  UAC is enabled")
                print("   → Run as Administrator for better access")
                return False
            else:
                print("   ✅ UAC is disabled")
                return True
                
        except:
            print("   ❓ Cannot read UAC settings")
            return True
    
    def check_virtualization_based_security(self):
        """Check for Virtualization-based Security"""
        print("\n🔐 Checking VBS (Virtualization-based Security):")
        
        try:
            result = subprocess.run([
                'powershell', '-Command',
                'Get-CimInstance -ClassName Win32_DeviceGuard -Namespace root\\Microsoft\\Windows\\DeviceGuard'
            ], capture_output=True, text=True, shell=True, timeout=15)
            
            if 'Running' in result.stdout or 'Enabled' in result.stdout:
                print("   ❌ VBS/Device Guard is ACTIVE")
                print("   → This provides hardware-level memory protection")
                print("   → Very difficult to bypass")
                return False
            else:
                print("   ✅ VBS/Device Guard not active")
                return True
                
        except Exception as e:
            print(f"   ❓ Cannot check VBS: {e}")
            return True
    
    def check_memory_integrity(self):
        """Check for Memory Integrity (HVCI)"""
        print("\n🧠 Checking Memory Integrity (HVCI):")
        
        try:
            result = subprocess.run([
                'powershell', '-Command',
                'Get-CimInstance -ClassName Win32_DeviceGuard -Namespace root\\Microsoft\\Windows\\DeviceGuard | Select-Object CodeIntegrityPolicyEnforcementStatus'
            ], capture_output=True, text=True, shell=True, timeout=15)
            
            if '1' in result.stdout or 'Enforced' in result.stdout:
                print("   ❌ Memory Integrity (HVCI) is ENFORCED")
                print("   → This blocks code injection and memory modification")
                print("   → Disable in Windows Security > Device Security")
                return False
            else:
                print("   ✅ Memory Integrity not enforced")
                return True
                
        except Exception as e:
            print(f"   ❓ Cannot check Memory Integrity: {e}")
            return True
    
    def provide_solutions(self):
        """Provide solutions for memory protection issues"""
        print("\n" + "=" * 60)
        print("💡 SOLUTIONS TO BYPASS MEMORY PROTECTION")
        print("=" * 60)
        
        print("\n🔧 IMMEDIATE FIXES:")
        print("1. Run as Administrator (most important!)")
        print("   → Right-click terminal/script → 'Run as administrator'")
        
        print("\n2. Disable Windows Defender temporarily:")
        print("   → Windows Security → Virus & threat protection")
        print("   → Turn off Real-time protection")
        
        print("\n3. Add exclusions to Windows Defender:")
        print("   → Windows Security → Virus & threat protection")
        print("   → Add exclusions for your hack folder")
        
        print("\n🛡️  ADVANCED FIXES:")
        print("4. Disable Memory Integrity:")
        print("   → Windows Security → Device Security")
        print("   → Core isolation → Memory integrity → Off")
        
        print("\n5. Disable VBS/Device Guard:")
        print("   → Run: bcdedit /set hypervisorlaunchtype off")
        print("   → Restart computer")
        
        print("\n6. Use alternative methods:")
        print("   → DLL injection instead of direct memory access")
        print("   → Process hollowing techniques")
        print("   → Kernel-mode drivers")
        
        print("\n⚠️  IMPORTANT NOTES:")
        print("• Disabling security features reduces system protection")
        print("• Re-enable protections after gaming")
        print("• Some games have their own anti-cheat protection")
        print("• Modern Windows has multiple layers of protection")

def main():
    """Main detection routine"""
    print("=" * 60)
    print("MEMORY PROTECTION DETECTOR")
    print("=" * 60)
    print("Checking what's blocking memory access...")
    print()
    
    detector = MemoryProtectionDetector()
    
    issues_found = []
    
    # Check all protection mechanisms
    if not detector.check_admin_privileges():
        issues_found.append("Not running as Administrator")
    
    if not detector.check_windows_defender():
        issues_found.append("Windows Defender blocking access")
    
    if not detector.check_antivirus_processes():
        issues_found.append("Antivirus/Security software detected")
    
    if not detector.check_dep_protection():
        issues_found.append("DEP protection active")
    
    if not detector.check_aslr_protection():
        issues_found.append("ASLR randomizing addresses")
    
    if not detector.check_uac_settings():
        issues_found.append("UAC limiting access")
    
    if not detector.check_virtualization_based_security():
        issues_found.append("VBS/Device Guard active")
    
    if not detector.check_memory_integrity():
        issues_found.append("Memory Integrity (HVCI) enforced")
    
    # Summary
    print("\n" + "=" * 60)
    print("DETECTION SUMMARY")
    print("=" * 60)
    
    if issues_found:
        print(f"❌ Found {len(issues_found)} protection mechanism(s):")
        for i, issue in enumerate(issues_found, 1):
            print(f"   {i}. {issue}")
        
        print(f"\n🚨 These are likely blocking your memory access!")
        detector.provide_solutions()
    else:
        print("✅ No obvious memory protection detected!")
        print("Memory access should work.")
        print("\nIf you're still having issues, the game might have:")
        print("• Built-in anti-cheat protection")
        print("• Custom memory protection")
        print("• Different process architecture")
    
    print("\n" + "=" * 60)
    print("DETECTION COMPLETE")
    print("=" * 60)

if __name__ == "__main__":
    main()
