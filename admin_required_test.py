#!/usr/bin/env python3
"""
Admin Required Test
==================

Simple test that requires administrator privileges.
This will tell you if running as admin fixes the memory access.

Author: Enhanced System
Version: 1.0
"""

import ctypes
import ctypes.wintypes
import sys

def check_admin():
    """Check if running as administrator"""
    try:
        is_admin = ctypes.windll.shell32.IsUserAnAdmin()
        return is_admin
    except:
        return False

def main():
    print("=" * 50)
    print("ADMINISTRATOR PRIVILEGE TEST")
    print("=" * 50)
    
    if check_admin():
        print("✅ SUCCESS: Running as Administrator!")
        print()
        print("Now you can try the Wolfenstein 2 hacks:")
        print("python wolf2_paused_compatible.py")
        print()
        print("The memory access should work much better now.")
    else:
        print("❌ FAILED: NOT running as Administrator!")
        print()
        print("TO FIX THIS:")
        print("1. Close this terminal/command prompt")
        print("2. Right-click on Command Prompt or PowerShell")
        print("3. Select 'Run as administrator'")
        print("4. Navigate to your folder:")
        print("   cd C:\\Users\\<USER>\\Desktop\\aimcock")
        print("5. Run this test again:")
        print("   python admin_required_test.py")
        print()
        print("Once you see 'SUCCESS', the hacks should work!")

if __name__ == "__main__":
    main()
