#!/usr/bin/env python3
"""
Wolfenstein 2: Paused Game Compatible
====================================

Works with paused games, minimized windows, and all game states.
Enhanced connection methods for maximum compatibility.

Author: Enhanced System
Version: PAUSED COMPATIBLE 1.0
"""

import pymem
import pymem.process
import psutil
import threading
import time
import tkinter as tk
from tkinter import ttk, scrolledtext
import ctypes
import ctypes.wintypes

class Wolf2PausedCompatible:
    """Wolfenstein 2 hacks that work with paused games"""
    
    def __init__(self):
        self.pm = None
        self.base_address = None
        self.process_id = None
        self.process_handle = None
        
        # All possible Wolfenstein 2 process names
        self.possible_processes = [
            "NewColossus_x64vk.exe",  # Fearless confirmed
            "BJ2_x64vk.exe",          # Steam Vulkan
            "BJ2_x64gl.exe",          # Steam OpenGL
            "BJ2.exe",                # Generic
            "Wolfenstein2.exe",       # Alternative
            "NewColossus.exe",        # Alternative
        ]
        
        # Fearless Revolution offsets
        self.fearless_offsets = {
            # Upgrade Kit Display & Pickup Logic
            'upgrade_display': [0x4DFE30, 0x4DFE34, 0x4DFE39, 0x4DFE3C, 0x4DFE3E, 
                               0x4DFE42, 0x4DFE43, 0x4DFE4A, 0x4DFE4E, 0x4DFE51, 0x4DFE55],
            
            # Upgrade Kit Usage Logic
            'upgrade_logic': [0x4DE376, 0x4DE37D, 0x4DE380, 0x4DE388, 0x4DE390, 0x4DE393,
                             0x4DE39A, 0x4DE39F, 0x4DE3A4, 0x4DE3A7, 0x4DE3AA, 0x4DE3AF,
                             0x4DE3B4, 0x4DE3B9, 0x4DE3BE, 0x4DE3C3, 0x4DE3CA],
        }
        
        self.features = {
            'godmode': False,
            'infinite_ammo': False,
            'noclip': False,
            'super_jump': False,
            'aimbot': False,
            'console_unlocked': False
        }
        
        self.status_callback = None
    
    def set_status_callback(self, callback):
        self.status_callback = callback
    
    def log(self, message):
        print(message)
        if self.status_callback:
            self.status_callback(message)
    
    def find_wolfenstein_process(self):
        """Enhanced process detection that works with paused games"""
        self.log("🔍 Enhanced process detection (works with paused games)...")
        
        found_processes = []
        
        for proc in psutil.process_iter(['pid', 'name', 'exe', 'status', 'memory_info']):
            try:
                proc_name = proc.info['name']
                
                for target_name in self.possible_processes:
                    if target_name.lower() in proc_name.lower():
                        status = proc.info.get('status', 'unknown')
                        memory_mb = proc.info.get('memory_info', {}).get('rss', 0) // 1024 // 1024
                        
                        found_processes.append({
                            'name': proc_name,
                            'pid': proc.info['pid'],
                            'exe': proc.info.get('exe', 'Unknown'),
                            'status': status,
                            'memory_mb': memory_mb
                        })
                        
                        self.log(f"✅ Found: {proc_name}")
                        self.log(f"   PID: {proc.info['pid']}")
                        self.log(f"   Status: {status}")
                        self.log(f"   Memory: {memory_mb} MB")
                        self.log(f"   Path: {proc.info.get('exe', 'Unknown')}")
                        
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                continue
        
        if not found_processes:
            self.log("❌ No Wolfenstein 2 processes found!")
            self.log("Make sure the game is running (even if paused/minimized)")
            return False
        
        # Use the first found process (or the one with most memory)
        target_proc = max(found_processes, key=lambda x: x['memory_mb'])
        
        self.process_id = target_proc['pid']
        self.process_name = target_proc['name']
        
        self.log(f"🎯 Selected: {self.process_name} (PID: {self.process_id})")
        return True
    
    def connect_with_multiple_methods(self):
        """Try multiple connection methods for paused games"""
        if not self.find_wolfenstein_process():
            return False
        
        self.log("🔌 Attempting connection with multiple methods...")
        
        # Method 1: pymem (preferred)
        try:
            self.log("   Method 1: pymem connection...")
            self.pm = pymem.Pymem(self.process_name)
            self.base_address = self.pm.base_address
            self.log(f"✅ pymem success! Base: 0x{self.base_address:X}")
            return True
        except Exception as e:
            self.log(f"   Method 1 failed: {e}")
        
        # Method 2: Direct Windows API
        try:
            self.log("   Method 2: Windows API connection...")
            
            # Get process handle with maximum permissions
            self.process_handle = ctypes.windll.kernel32.OpenProcess(
                0x1F0FFF,  # PROCESS_ALL_ACCESS
                False, 
                self.process_id
            )
            
            if not self.process_handle:
                raise Exception("Failed to get process handle")
            
            # Get base address using EnumProcessModules
            hModules = (ctypes.wintypes.HMODULE * 1024)()
            cbNeeded = ctypes.wintypes.DWORD()
            
            result = ctypes.windll.psapi.EnumProcessModules(
                self.process_handle,
                ctypes.byref(hModules),
                ctypes.sizeof(hModules),
                ctypes.byref(cbNeeded)
            )
            
            if result:
                self.base_address = hModules[0]
                self.log(f"✅ Windows API success! Base: 0x{self.base_address:X}")
                return True
            else:
                raise Exception("Failed to enumerate modules")
                
        except Exception as e:
            self.log(f"   Method 2 failed: {e}")
        
        # Method 3: Process memory scanning
        try:
            self.log("   Method 3: Memory scanning...")
            
            # Use psutil to get memory maps
            process = psutil.Process(self.process_id)
            memory_maps = process.memory_maps()
            
            for mmap in memory_maps:
                if hasattr(mmap, 'path') and mmap.path and self.process_name in mmap.path:
                    # Extract base address from memory map
                    addr_str = str(mmap).split()[1] if len(str(mmap).split()) > 1 else "0x140000000"
                    
                    if '-' in addr_str:
                        base_str = addr_str.split('-')[0]
                        self.base_address = int(base_str, 16)
                        self.log(f"✅ Memory scan success! Base: 0x{self.base_address:X}")
                        return True
                        
        except Exception as e:
            self.log(f"   Method 3 failed: {e}")
        
        self.log("❌ All connection methods failed!")
        return False
    
    def test_memory_access_paused(self):
        """Test memory access specifically for paused games"""
        self.log("🧪 Testing memory access (paused game compatible)...")
        
        if not self.base_address:
            return False
        
        try:
            # Test reading from Fearless offsets
            test_addresses = [
                self.base_address + self.fearless_offsets['upgrade_display'][0],
                self.base_address + self.fearless_offsets['upgrade_logic'][0],
                self.base_address + 0x1000000,  # Safe test area
            ]
            
            successful_reads = 0
            
            for addr in test_addresses:
                try:
                    if self.pm:
                        # Use pymem
                        value = self.pm.read_int(addr)
                    else:
                        # Use Windows API
                        buffer = ctypes.create_string_buffer(4)
                        bytes_read = ctypes.c_size_t(0)
                        
                        result = ctypes.windll.kernel32.ReadProcessMemory(
                            self.process_handle,
                            ctypes.c_void_p(addr),
                            buffer,
                            4,
                            ctypes.byref(bytes_read)
                        )
                        
                        if result and bytes_read.value == 4:
                            value = int.from_bytes(buffer.raw, byteorder='little', signed=True)
                        else:
                            continue
                    
                    self.log(f"   Read 0x{addr:X} = {value}")
                    successful_reads += 1
                    
                except Exception as e:
                    self.log(f"   Failed to read 0x{addr:X}: {e}")
                    continue
            
            if successful_reads > 0:
                self.log(f"✅ Memory access working! ({successful_reads}/3 reads successful)")
                return True
            else:
                self.log("❌ No successful memory reads")
                return False
                
        except Exception as e:
            self.log(f"❌ Memory test error: {e}")
            return False
    
    def write_memory_safe(self, address, value):
        """Safe memory writing that works with paused games"""
        try:
            if self.pm:
                # Use pymem
                self.pm.write_int(address, value)
                return True
            elif self.process_handle:
                # Use Windows API
                data = value.to_bytes(4, byteorder='little', signed=True)
                bytes_written = ctypes.c_size_t(0)
                
                result = ctypes.windll.kernel32.WriteProcessMemory(
                    self.process_handle,
                    ctypes.c_void_p(address),
                    data,
                    len(data),
                    ctypes.byref(bytes_written)
                )
                
                return result and bytes_written.value == len(data)
            
            return False
            
        except Exception as e:
            return False
    
    def start_paused_godmode(self):
        """God mode that works with paused games"""
        if self.features['godmode']:
            return
        
        self.features['godmode'] = True
        self.log("🛡️  Paused-Compatible God Mode ACTIVATED")
        
        def godmode_worker():
            while self.features['godmode']:
                try:
                    # Use Fearless display addresses for health
                    for offset in self.fearless_offsets['upgrade_display'][:5]:  # First 5 addresses
                        addr = self.base_address + offset
                        
                        # Try to boost health-like values
                        try:
                            if self.pm:
                                current = self.pm.read_int(addr)
                            else:
                                buffer = ctypes.create_string_buffer(4)
                                bytes_read = ctypes.c_size_t(0)
                                
                                result = ctypes.windll.kernel32.ReadProcessMemory(
                                    self.process_handle, ctypes.c_void_p(addr),
                                    buffer, 4, ctypes.byref(bytes_read)
                                )
                                
                                if result and bytes_read.value == 4:
                                    current = int.from_bytes(buffer.raw, byteorder='little', signed=True)
                                else:
                                    continue
                            
                            # If looks like health, boost it
                            if 1 <= current <= 200:
                                self.write_memory_safe(addr, 999)
                                self.log(f"🛡️  Health boosted: {current} → 999")
                                
                        except:
                            continue
                    
                    time.sleep(0.5)  # Slower for paused games
                    
                except Exception as e:
                    self.log(f"Godmode error: {e}")
                    time.sleep(2.0)
        
        threading.Thread(target=godmode_worker, daemon=True).start()
    
    def unlock_console_paused(self):
        """Unlock console for paused games"""
        if self.features['console_unlocked']:
            return True
        
        try:
            self.log("🔓 Unlocking console (paused game method)...")
            
            # Patch upgrade logic addresses to enable console
            patched_count = 0
            
            for offset in self.fearless_offsets['upgrade_logic'][:10]:  # First 10 addresses
                try:
                    addr = self.base_address + offset
                    
                    # Enable console/cheat mode
                    if self.write_memory_safe(addr, 1):
                        patched_count += 1
                        
                except:
                    continue
            
            if patched_count > 0:
                self.features['console_unlocked'] = True
                self.log(f"✅ Console unlocked! ({patched_count} patches applied)")
                self.log("   Press ~ in-game for console (even when paused!)")
                return True
            else:
                self.log("❌ Console unlock failed")
                return False
                
        except Exception as e:
            self.log(f"❌ Console unlock error: {e}")
            return False
    
    def start_feature(self, feature_name):
        """Start features for paused games"""
        if feature_name == 'godmode':
            self.start_paused_godmode()
        elif feature_name == 'console_unlock':
            self.unlock_console_paused()
    
    def stop_feature(self, feature_name):
        """Stop a feature"""
        if feature_name in self.features:
            self.features[feature_name] = False
            self.log(f"⏹️  {feature_name.upper()} stopped")
    
    def stop_all_features(self):
        """Stop all features"""
        for feature in self.features:
            if feature != 'console_unlocked':
                self.features[feature] = False
        self.log("⏹️  All features stopped")

class PausedCompatibleGUI:
    """GUI for paused game compatibility"""
    
    def __init__(self):
        self.hacks = Wolf2PausedCompatible()
        self.root = tk.Tk()
        self.root.title("Wolfenstein 2 - PAUSED GAME COMPATIBLE")
        self.root.geometry("700x600")
        self.root.configure(bg='#1a1a2e')
        
        self.create_interface()
        self.hacks.set_status_callback(self.add_log)
    
    def create_interface(self):
        """Create paused-compatible GUI"""
        # Title
        title = tk.Label(self.root, text="⏸️  PAUSED GAME COMPATIBLE", 
                        font=('Arial', 18, 'bold'), fg='#00ff41', bg='#1a1a2e')
        title.pack(pady=10)
        
        subtitle = tk.Label(self.root, text="Works with Paused, Minimized, and Background Games", 
                           font=('Arial', 12), fg='#ffaa00', bg='#1a1a2e')
        subtitle.pack(pady=5)
        
        # Connection section
        conn_frame = tk.Frame(self.root, bg='#1a1a2e')
        conn_frame.pack(pady=15)
        
        self.connect_btn = tk.Button(conn_frame, text="🔌 ENHANCED CONNECTION", 
                                    bg='#00ff41', fg='black', font=('Arial', 14, 'bold'),
                                    width=25, height=2, command=self.enhanced_connect)
        self.connect_btn.pack(pady=5)
        
        self.status_label = tk.Label(conn_frame, text="Ready for paused game connection", 
                                    font=('Arial', 11), fg='#00ff41', bg='#1a1a2e')
        self.status_label.pack(pady=5)
        
        # Features for paused games
        features_frame = tk.LabelFrame(self.root, text="⏸️  PAUSED GAME FEATURES", 
                                      fg='#00ff41', bg='#1a1a2e', font=('Arial', 12, 'bold'))
        features_frame.pack(fill='x', padx=20, pady=10)
        
        self.feature_buttons = {}
        
        # Console unlock (most important for paused games)
        self.console_btn = tk.Button(features_frame, text="🔓 UNLOCK CONSOLE (PAUSED)", 
                                    bg='#ff6600', fg='white', font=('Arial', 12, 'bold'),
                                    width=30, command=self.unlock_console, state='disabled')
        self.console_btn.pack(pady=10)
        
        # Other features
        other_features = [
            ("🛡️  God Mode (Paused)", 'godmode', '#ff0066'),
        ]
        
        for text, feature, color in other_features:
            btn = tk.Button(features_frame, text=text, bg=color, fg='white',
                           font=('Arial', 11, 'bold'), width=25,
                           command=lambda f=feature: self.toggle_feature(f),
                           state='disabled')
            btn.pack(pady=5)
            self.feature_buttons[feature] = btn
        
        # Instructions for paused games
        inst_frame = tk.LabelFrame(self.root, text="⏸️  PAUSED GAME INSTRUCTIONS", 
                                  fg='#ffff00', bg='#1a1a2e', font=('Arial', 11, 'bold'))
        inst_frame.pack(fill='x', padx=20, pady=10)
        
        instructions = [
            "✅ Game can be PAUSED - this system works with paused games!",
            "✅ Game can be MINIMIZED - works in background",
            "✅ Game can be ALT-TABBED - no need to focus game window",
            "",
            "1. Start Wolfenstein 2 (any state: paused, minimized, etc.)",
            "2. Click 'ENHANCED CONNECTION' - tries multiple methods",
            "3. Click 'UNLOCK CONSOLE' - enables console even when paused",
            "4. Press ~ in-game for console (works even when paused!)",
            "5. Console commands: god, noclip, notarget"
        ]
        
        for inst in instructions:
            color = '#00ff41' if inst.startswith('✅') else '#ffffff'
            tk.Label(inst_frame, text=inst, fg=color, bg='#1a1a2e',
                    font=('Arial', 9)).pack(anchor='w', padx=10, pady=1)
        
        # Status log
        log_frame = tk.LabelFrame(self.root, text="📊 Connection Status", 
                                 fg='#00ffff', bg='#1a1a2e', font=('Arial', 11, 'bold'))
        log_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=8, bg='#000000', fg='#00ff41',
                                                 font=('Consolas', 9))
        self.log_text.pack(fill='both', expand=True, padx=5, pady=5)
    
    def add_log(self, message):
        self.log_text.insert(tk.END, message + '\n')
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def enhanced_connect(self):
        """Enhanced connection for paused games"""
        self.connect_btn.config(state='disabled', text='CONNECTING...', bg='#ffaa00')
        self.status_label.config(text='Trying multiple connection methods...', fg='#ffaa00')
        
        def connect_worker():
            if self.hacks.connect_with_multiple_methods():
                if self.hacks.test_memory_access_paused():
                    # Success
                    self.connect_btn.config(bg='#00ff41', text='✅ CONNECTED (PAUSED OK)', state='disabled')
                    self.status_label.config(text='🎉 Connected! Works with paused games!', fg='#00ff41')
                    
                    # Enable buttons
                    self.console_btn.config(state='normal')
                    for btn in self.feature_buttons.values():
                        btn.config(state='normal')
                else:
                    # Partial success
                    self.connect_btn.config(bg='#ff8800', text='⚠️  PARTIAL CONNECTION', state='normal')
                    self.status_label.config(text='Connected but memory access limited', fg='#ff8800')
            else:
                # Failed
                self.connect_btn.config(bg='#ff0000', text='❌ CONNECTION FAILED', state='normal')
                self.status_label.config(text='No Wolfenstein 2 process found', fg='#ff0000')
        
        threading.Thread(target=connect_worker, daemon=True).start()
    
    def unlock_console(self):
        """Unlock console for paused games"""
        self.console_btn.config(state='disabled', text='UNLOCKING...', bg='#ffaa00')
        
        def unlock_worker():
            if self.hacks.unlock_console_paused():
                self.console_btn.config(bg='#00ff41', text='✅ CONSOLE UNLOCKED', state='disabled')
            else:
                self.console_btn.config(bg='#ff8800', text='⚠️  PARTIAL UNLOCK', state='normal')
        
        threading.Thread(target=unlock_worker, daemon=True).start()
    
    def toggle_feature(self, feature_name):
        """Toggle feature for paused games"""
        btn = self.feature_buttons[feature_name]
        
        if self.hacks.features[feature_name]:
            self.hacks.stop_feature(feature_name)
            btn.config(text=btn.cget('text').replace('✅ ', ''), bg='#ff0066')
        else:
            self.hacks.start_feature(feature_name)
            btn.config(text=f"✅ {btn.cget('text')}", bg='#00aa00')
    
    def run(self):
        self.add_log("⏸️  PAUSED GAME COMPATIBLE SYSTEM - Ready!")
        self.add_log("This system works with paused, minimized, and background games")
        self.add_log("Your game can be paused - no problem!")
        self.add_log("Click 'ENHANCED CONNECTION' to begin")
        self.root.mainloop()

if __name__ == "__main__":
    print("=" * 70)
    print("WOLFENSTEIN 2 - PAUSED GAME COMPATIBLE SYSTEM")
    print("=" * 70)
    print("Works with paused, minimized, and background games!")
    
    app = PausedCompatibleGUI()
    app.run()
