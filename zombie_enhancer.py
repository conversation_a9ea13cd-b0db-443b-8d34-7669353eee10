#!/usr/bin/env python3
"""
Nazi Zombies Enhancer
====================

Specifically for COD WWII Nazi Zombies mode.
Uses zombie-specific memory offsets.

Author: Enhanced System
Version: 1.0
"""

import tkinter as tk
import ctypes
import struct
import psutil
import threading
import time
import pygame

class ZombieEnhancer:
    """Nazi Zombies specific enhancer"""
    
    def __init__(self):
        # Find COD process
        self.process_id = None
        self.process_handle = None
        
        for proc in psutil.process_iter(['pid', 'name']):
            if 's2_mp64_ship.exe' in proc.info['name']:
                self.process_id = proc.info['pid']
                break
        
        if self.process_id:
            self.process_handle = ctypes.windll.kernel32.OpenProcess(0x1F0FFF, False, self.process_id)
            print(f"[ZOMBIE] Found COD WWII: PID {self.process_id}")
        
        # NAZI ZOMBIES SPECIFIC OFFSETS
        self.base = 0x140000000  # We'll test this
        self.offsets = {
            # Nazi Zombies specific (from your data)
            'zombie_array': 0xA0D35B0,        # Zombie entities
            'player_health_zombies': 0xA2D7DC8,  # Your health in zombies mode
            'player_username': 0xE4650B0,      # Your username
            'special_ability': 0xA4B1888,      # Special ability
            
            # Weapon offsets for zombies
            'weapon_slot_1': 0xA0C7388 + 0x780,
            'weapon_slot_2': 0xA0C7388 + 0x7C8,
            'weapon_slot_3': 0xA0C7388 + 0x7B4,
        }
        
        # Feature states
        self.godmode_active = False
        self.infinite_ammo_active = False
        self.super_jump_active = False
        
        # Initialize gamepad
        pygame.init()
        pygame.joystick.init()
        self.gamepad = None
        if pygame.joystick.get_count() > 0:
            self.gamepad = pygame.joystick.Joystick(0)
            self.gamepad.init()
            print(f"[ZOMBIE] Gamepad: {self.gamepad.get_name()}")
    
    def test_zombie_memory(self):
        """Test if we can read zombie-specific memory"""
        print("[ZOMBIE] Testing Nazi Zombies memory access...")
        
        if not self.process_handle:
            print("[ZOMBIE] No process handle!")
            return False
        
        try:
            # Test zombie-specific health address
            health_addr = self.base + self.offsets['player_health_zombies']
            health = self.read_int(health_addr)
            print(f"[ZOMBIE] Your health (zombies mode): {health}")
            
            # Test zombie array
            zombie_array_addr = self.base + self.offsets['zombie_array']
            zombie_data = self.read_int(zombie_array_addr)
            print(f"[ZOMBIE] Zombie array data: {zombie_data}")
            
            # Test username
            username_addr = self.base + self.offsets['player_username']
            username_data = self.read_int(username_addr)
            print(f"[ZOMBIE] Username data: {username_data}")
            
            if health > 0 and health <= 200:
                print("[ZOMBIE] ✅ Health looks valid for zombies!")
                return True
            elif health == 0:
                print("[ZOMBIE] ❌ Health is 0 - not in active zombies match?")
                return False
            else:
                print(f"[ZOMBIE] ❓ Unexpected health: {health}")
                return False
                
        except Exception as e:
            print(f"[ZOMBIE] Memory test error: {e}")
            return False
    
    def start_zombie_godmode(self):
        """Godmode for Nazi Zombies"""
        if self.godmode_active:
            return
        
        self.godmode_active = True
        print("[ZOMBIE GODMODE] STARTING - Infinite health for zombies")
        
        def godmode_loop():
            while self.godmode_active:
                try:
                    # Write to zombie-specific health address
                    health_addr = self.base + self.offsets['player_health_zombies']
                    current_health = self.read_int(health_addr)
                    
                    if 0 < current_health < 150:  # If health is low
                        self.write_int(health_addr, 200)  # Set to max
                        print(f"[ZOMBIE GODMODE] Health: {current_health} → 200")
                    
                    time.sleep(0.2)
                    
                except Exception as e:
                    print(f"[ZOMBIE GODMODE] Error: {e}")
                    time.sleep(1.0)
        
        threading.Thread(target=godmode_loop, daemon=True).start()
    
    def start_zombie_infinite_ammo(self):
        """Infinite ammo for Nazi Zombies"""
        if self.infinite_ammo_active:
            return
        
        self.infinite_ammo_active = True
        print("[ZOMBIE AMMO] STARTING - Infinite ammo for zombies")
        
        def ammo_loop():
            while self.infinite_ammo_active:
                try:
                    # Set ammo for all weapon slots
                    for i, slot_name in enumerate(['weapon_slot_1', 'weapon_slot_2', 'weapon_slot_3']):
                        ammo_addr = self.base + self.offsets[slot_name]
                        self.write_int(ammo_addr, 999)
                    
                    time.sleep(0.3)
                    
                except Exception as e:
                    print(f"[ZOMBIE AMMO] Error: {e}")
                    time.sleep(1.0)
        
        threading.Thread(target=ammo_loop, daemon=True).start()
    
    def start_zombie_super_jump(self):
        """Super jump for Nazi Zombies"""
        if self.super_jump_active:
            return
        
        self.super_jump_active = True
        print("[ZOMBIE JUMP] STARTING - Press A for super jump")
        
        def jump_loop():
            last_a = False
            
            while self.super_jump_active:
                try:
                    if self.gamepad:
                        pygame.event.pump()
                        a_pressed = self.gamepad.get_button(0)  # A button
                        
                        if a_pressed and not last_a:  # Just pressed
                            # Try different position addresses for zombies
                            pos_addresses = [
                                0x140000000 + 0x0A0C7388 + 0x84,  # Original Z offset
                                0x140000000 + 0xA2D7DC8 + 0x84,   # Health base + Z offset
                            ]
                            
                            for pos_addr in pos_addresses:
                                current_z = self.read_float(pos_addr)
                                if current_z != 0:
                                    new_z = current_z + 100.0
                                    if self.write_float(pos_addr, new_z):
                                        print(f"[ZOMBIE JUMP] Executed! {current_z:.1f} → {new_z:.1f}")
                                        break
                        
                        last_a = a_pressed
                    
                    time.sleep(0.01)
                    
                except Exception as e:
                    print(f"[ZOMBIE JUMP] Error: {e}")
                    time.sleep(0.1)
        
        threading.Thread(target=jump_loop, daemon=True).start()
    
    def read_int(self, address):
        try:
            buffer = ctypes.create_string_buffer(4)
            bytes_read = ctypes.c_size_t(0)
            result = ctypes.windll.kernel32.ReadProcessMemory(
                self.process_handle, ctypes.c_void_p(address),
                buffer, 4, ctypes.byref(bytes_read))
            if result and bytes_read.value == 4:
                return struct.unpack('<i', buffer.raw)[0]
        except Exception:
            pass
        return 0
    
    def read_float(self, address):
        try:
            buffer = ctypes.create_string_buffer(4)
            bytes_read = ctypes.c_size_t(0)
            result = ctypes.windll.kernel32.ReadProcessMemory(
                self.process_handle, ctypes.c_void_p(address),
                buffer, 4, ctypes.byref(bytes_read))
            if result and bytes_read.value == 4:
                return struct.unpack('<f', buffer.raw)[0]
        except Exception:
            pass
        return 0.0
    
    def write_int(self, address, value):
        try:
            data = struct.pack('<i', value)
            bytes_written = ctypes.c_size_t(0)
            result = ctypes.windll.kernel32.WriteProcessMemory(
                self.process_handle, ctypes.c_void_p(address),
                data, len(data), ctypes.byref(bytes_written))
            return result and bytes_written.value == len(data)
        except Exception:
            return False
    
    def write_float(self, address, value):
        try:
            data = struct.pack('<f', value)
            bytes_written = ctypes.c_size_t(0)
            result = ctypes.windll.kernel32.WriteProcessMemory(
                self.process_handle, ctypes.c_void_p(address),
                data, len(data), ctypes.byref(bytes_written))
            return result and bytes_written.value == len(data)
        except Exception:
            return False

class ZombieGUI:
    """Simple Nazi Zombies interface"""
    
    def __init__(self):
        self.enhancer = ZombieEnhancer()
        self.root = tk.Tk()
        self.root.title("Nazi Zombies Enhancer")
        self.root.geometry("400x500")
        self.root.configure(bg='#0d1b0d')
        
        self.create_interface()
    
    def create_interface(self):
        # Title
        tk.Label(self.root, text="NAZI ZOMBIES ENHANCER", 
                font=('Arial', 16, 'bold'), fg='red', bg='#0d1b0d').pack(pady=10)
        
        # Warning
        tk.Label(self.root, text="⚠️ START A NAZI ZOMBIES MATCH FIRST!", 
                font=('Arial', 12, 'bold'), fg='yellow', bg='#0d1b0d').pack(pady=5)
        
        # Test button
        tk.Button(self.root, text="TEST ZOMBIE MEMORY", bg='purple', fg='white',
                 font=('Arial', 12, 'bold'), command=self.test_memory).pack(pady=10)
        
        # Features
        tk.Button(self.root, text="ZOMBIE GODMODE", bg='red', fg='white',
                 font=('Arial', 12, 'bold'), command=self.toggle_godmode).pack(pady=5)
        
        tk.Button(self.root, text="INFINITE AMMO", bg='orange', fg='black',
                 font=('Arial', 12, 'bold'), command=self.toggle_ammo).pack(pady=5)
        
        tk.Button(self.root, text="SUPER JUMP", bg='blue', fg='white',
                 font=('Arial', 12, 'bold'), command=self.toggle_jump).pack(pady=5)
        
        # Status
        self.status = tk.Label(self.root, text="Ready - Start zombies match first!", 
                              fg='white', bg='#0d1b0d')
        self.status.pack(pady=20)
        
        # Instructions
        instructions = [
            "1. Start Nazi Zombies match",
            "2. Wait until you're playing", 
            "3. Test memory first",
            "4. Then enable features"
        ]
        
        for inst in instructions:
            tk.Label(self.root, text=inst, fg='lime', bg='#0d1b0d').pack(anchor='w', padx=20)
    
    def test_memory(self):
        if self.enhancer.test_zombie_memory():
            self.status.config(text="✅ Memory access working!", fg='lime')
        else:
            self.status.config(text="❌ Memory access failed - start zombies match!", fg='red')
    
    def toggle_godmode(self):
        if not self.enhancer.godmode_active:
            self.enhancer.start_zombie_godmode()
            self.status.config(text="ZOMBIE GODMODE ACTIVE", fg='red')
        else:
            self.enhancer.godmode_active = False
            self.status.config(text="Zombie godmode stopped", fg='white')
    
    def toggle_ammo(self):
        if not self.enhancer.infinite_ammo_active:
            self.enhancer.start_zombie_infinite_ammo()
            self.status.config(text="INFINITE AMMO ACTIVE", fg='orange')
        else:
            self.enhancer.infinite_ammo_active = False
            self.status.config(text="Infinite ammo stopped", fg='white')
    
    def toggle_jump(self):
        if not self.enhancer.super_jump_active:
            self.enhancer.start_zombie_super_jump()
            self.status.config(text="SUPER JUMP ACTIVE - Press A", fg='blue')
        else:
            self.enhancer.super_jump_active = False
            self.status.config(text="Super jump stopped", fg='white')
    
    def run(self):
        print("[ZOMBIE GUI] Nazi Zombies enhancer ready!")
        print("[ZOMBIE GUI] IMPORTANT: Start a zombies match first!")
        self.root.mainloop()

if __name__ == "__main__":
    app = ZombieGUI()
    app.run()

