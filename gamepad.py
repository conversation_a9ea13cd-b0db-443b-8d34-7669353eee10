#!/usr/bin/env python3
"""
Simple Gamepad Controller
========================

Xbox controller input detection for COD WWII enhancements.

Author: Enhanced System
Version: 1.0
"""

import pygame
import time

class GamepadController:
    """Simple Xbox controller handler"""
    
    def __init__(self):
        pygame.init()
        pygame.joystick.init()
        
        self.controller = None
        self.connected = False
        
        # Connect to first controller
        if pygame.joystick.get_count() > 0:
            self.controller = pygame.joystick.Joystick(0)
            self.controller.init()
            self.connected = True
            print(f"[GAMEPAD] Connected: {self.controller.get_name()}")
        else:
            print("[GAMEPAD] No controller detected")
    
    def is_connected(self):
        """Check if controller is connected"""
        return self.connected
    
    def get_button(self, button_id):
        """Get button state (0=A, 1=B, 2=X, 3=Y, etc.)"""
        if not self.connected:
            return False
        
        try:
            pygame.event.pump()
            return self.controller.get_button(button_id)
        except Exception:
            return False
    
    def get_trigger(self, trigger):
        """Get trigger value (RT=5, LT=4)"""
        if not self.connected:
            return 0.0
        
        try:
            pygame.event.pump()
            return self.controller.get_axis(trigger)
        except Exception:
            return 0.0
    
    def get_stick(self, stick):
        """Get stick values - returns (x, y) tuple"""
        if not self.connected:
            return (0.0, 0.0)
        
        try:
            pygame.event.pump()
            if stick == 'left':
                return (self.controller.get_axis(0), self.controller.get_axis(1))
            elif stick == 'right':
                return (self.controller.get_axis(2), self.controller.get_axis(3))
        except Exception:
            return (0.0, 0.0)
        
        return (0.0, 0.0)
    
    def is_rt_pressed(self):
        """Check if right trigger is pressed"""
        return self.get_trigger(5) > 0.3
    
    def is_lt_pressed(self):
        """Check if left trigger is pressed"""
        return self.get_trigger(4) > 0.3
    
    def is_a_pressed(self):
        """Check if A button is pressed"""
        return self.get_button(0)






